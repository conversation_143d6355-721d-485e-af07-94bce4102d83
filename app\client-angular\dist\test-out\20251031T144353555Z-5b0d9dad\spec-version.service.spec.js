import {
  TestBed
} from "./chunk-JBEFZW42.js";
import {
  HttpClient,
  VersionService
} from "./chunk-2BWCZPUI.js";
import "./chunk-PRSR237C.js";
import {
  of,
  provideZonelessChangeDetection,
  throwError
} from "./chunk-SG4F3HSD.js";

// src/main/angular/services/version.service.spec.ts
import { describe, it, expect, vi, beforeEach } from "vitest";
vi.mock("../app.routes", () => ({
  backendUrl: () => "http://localhost:8080"
}));
vi.mock("../utils/log", () => ({
  tapLog: () => (source) => source
}));
describe("VersionService", () => {
  let service;
  let httpClientMock;
  const mockVersion = {
    version: "1.2.0"
  };
  beforeEach(() => {
    httpClientMock = {
      get: vi.fn()
    };
    TestBed.configureTestingModule({
      providers: [
        VersionService,
        { provide: HttpClient, useValue: httpClientMock },
        provideZonelessChangeDetection()
      ]
    });
    service = TestBed.inject(VersionService);
  });
  it("should be created", () => {
    expect(service).toBeTruthy();
    expect(service["httpClient"]).toBeDefined();
  });
  describe("loadVersion", () => {
    it("should load version successfully", (done) => {
      httpClientMock.get.mockReturnValue(of(mockVersion));
      service.loadVersion().subscribe({
        next: (body) => {
          expect(httpClientMock.get).toHaveBeenCalledWith("http://localhost:8080/version");
          expect(body).toEqual(mockVersion);
        }
      });
    });
    it("should handle errors gracefully", (done) => {
      const notFoundError = {
        status: 404,
        message: "Not Found"
      };
      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));
      service.loadVersion().subscribe({
        error: (err) => {
          expect(httpClientMock.get).toHaveBeenCalledWith("http://localhost:8080/version");
          expect(err).toBe(notFoundError);
        }
      });
    });
  });
});
//# sourceMappingURL=spec-version.service.spec.js.map
