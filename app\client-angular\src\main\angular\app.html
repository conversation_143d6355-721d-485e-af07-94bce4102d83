<article class="flex flex-col h-screen">
  <header class="flex justify-between items-center bg-gray-200 p-2 h-12">
    <nav class="flex flex-row text-lg text-gray-600 gap-1">
      <app-icon (click)="onMenuToggle()" [open]="menuVisible()" />
      <app-logo (click)="onMenuClose()" routerLink="/home" />
    </nav>
    <nav class="flex flex-row text-lg text-gray-600 gap-1">
      <a (click)="onMenuClose()" routerLink="/help">?</a>
    </nav>
  </header>
  <main class="flex-1 overflow-y-auto">
    @if (menuVisible()) {
      <aside class="w-72 w-full h-screen fixed z-10">
        <nav class="absolute flex w-full h-full pointer-events-auto bg-white">
          <div class="w-full">
            <div class="flex flex-col p-2 text-gray-600 gap-1">
              <span class="text-lg text-gray-900 capitalize">Client</span>
              <div class="flex flex-col p-4 text-gray-600 gap-1">
                <a (click)="onMenuClose()" routerLink="/owner">Owner</a>
                <a (click)="onMenuClose()" routerLink="/pet">Pet</a>
              </div>
            </div>
            <div class="flex flex-col p-2 text-gray-600 gap-1">
              <span class="text-lg text-gray-900 capitalize">Clinic</span>
              <div class="flex flex-col p-4 text-gray-600 gap-1">
                <a (click)="onMenuClose()" routerLink="/visit">Visit </a>
                <a (click)="onMenuClose()" routerLink="/vet">Vet</a>
                <a (click)="onMenuClose()" routerLink="/enum/skill">Skill</a>
                <a (click)="onMenuClose()" routerLink="/enum/species"
                  >Species</a
                >
              </div>
            </div>
          </div>
        </nav>
      </aside>
    } @else {
      <router-outlet />
    }
  </main>
  <footer class="flex justify-center bg-gray-200 p-2 h-10">
    <nav class="flex flex-row text-sm text-gray-600 gap-1">
      <a (click)="onMenuClose()" routerLink="/help">Impressum</a>
    </nav>
  </footer>
</article>
