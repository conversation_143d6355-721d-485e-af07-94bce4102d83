plugins {
    id 'java'
}

repositories {
    mavenCentral()
}

dependencies {
    // https://projectlombok.org
    implementation('org.projectlombok:lombok:1.18.42')
    annotationProcessor('org.projectlombok:lombok:1.18.42')
    // https://projects.eclipse.org/projects/technology.jgit
    implementation('org.eclipse.jgit:org.eclipse.jgit:7.4.0.202509020913-r')}
dependencies {
    // https://junit.org/junit5
    testImplementation('org.junit.jupiter:junit-jupiter:5.14.0')
    testRuntimeOnly('org.junit.platform:junit-platform-launcher')
}

test {
    useJUnitPlatform()
}
