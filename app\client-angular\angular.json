{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "app", "projects": {"client-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"path": "src/main/angular", "skipTests": true, "inlineStyle": true, "inlineTemplate": false}, "@schematics/angular:directive": {"path": "src/main/angular", "skipTests": true}, "@schematics/angular:pipe": {"path": "src/main/angular/pipes", "skipTests": true}, "@schematics/angular:service": {"path": "src/main/angular/services", "type": "Service", "skipTests": true}}, "root": "", "sourceRoot": "src/main/angular", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main/angular/main.ts", "polyfills": [], "tsConfig": "tsconfig.main.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/main/angular/styles.css"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all", "outputPath": "build/generated"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular/build:unit-test", "options": {"tsConfig": "tsconfig.spec.json", "runner": "vitest", "buildTarget": "::development"}}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "client-angular:build:production"}, "development": {"buildTarget": "client-angular:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}}}}, "cli": {"analytics": false}}