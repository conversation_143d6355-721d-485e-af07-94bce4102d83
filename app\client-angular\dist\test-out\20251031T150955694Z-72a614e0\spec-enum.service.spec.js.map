{"version": 3, "sources": ["src/main/angular/services/enum.service.spec.ts"], "sourcesContent": ["import { describe, it, expect, vi, beforeEach } from \"vitest\";\nimport { provideZonelessChangeDetection } from \"@angular/core\";\nimport { TestBed } from \"@angular/core/testing\";\nimport { HttpClient } from \"@angular/common/http\";\nimport { of, throwError } from \"rxjs\";\nimport { EnumService } from \"./enum.service\";\nimport { type EnumItem } from \"../types/enum.type\";\n\nconst ALLSPECIES: EnumItem[] = [\n  {\n    code: 1,\n    name: \"<PERSON>\",\n    text: \"A dog is an animal of the species Canis lupus familiaris.\",\n  },\n  {\n    code: 2,\n    name: \"Cat\",\n    text: \"A cat is an animal of the species Felis catus.\",\n  },\n];\n\ndescribe(\"EnumService\", () => {\n  let enumService: EnumService;\n  let httpClientMock: any;\n\n  beforeEach(() => {\n    httpClientMock = {\n      get: vi.fn(),\n      post: vi.fn(),\n      put: vi.fn(),\n      delete: vi.fn(),\n    };\n    TestBed.configureTestingModule({\n      providers: [\n        EnumService,\n        { provide: HttpClient, useValue: httpClientMock },\n        provideZonelessChangeDetection(),\n      ],\n    });\n    enumService = TestBed.inject(EnumService);\n  });\n\n  it(\"should be created\", () => {\n    expect(enumService).toBeTruthy();\n    expect(enumService[\"httpClient\"]).toBeDefined();\n  });\n\n  describe(\"loadAllEnum\", () => {\n    it(\"should load enum items successfully\", () => {\n      httpClientMock.get.mockReturnValue(\n        of({\n          content: ALLSPECIES,\n        })\n      );\n      enumService.loadAllEnum(\"species\").subscribe({\n        next: (allItem) => {\n          expect(allItem).toEqual(ALLSPECIES);\n        },\n      });\n    });\n\n    it(\"should handle errors gracefully\", () => {\n      const notFoundError = {\n        status: 404,\n        message: \"Not Found\",\n      };\n      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));\n      enumService.loadAllEnum(\"species\").subscribe({\n        error: (err) => {\n          expect(err).toBe(notFoundError);\n        },\n      });\n    });\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SAAS,UAAU,IAAI,QAAQ,IAAI,kBAAkB;AAQrD,IAAM,aAAyB;EAC7B;IACE,MAAM;IACN,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;IACN,MAAM;;;AAIV,SAAS,eAAe,MAAK;AAC3B,MAAI;AACJ,MAAI;AAEJ,aAAW,MAAK;AACd,qBAAiB;MACf,KAAK,GAAG,GAAE;MACV,MAAM,GAAG,GAAE;MACX,KAAK,GAAG,GAAE;MACV,QAAQ,GAAG,GAAE;;AAEf,YAAQ,uBAAuB;MAC7B,WAAW;QACT;QACA,EAAE,SAAS,YAAY,UAAU,eAAc;QAC/C,+BAA8B;;KAEjC;AACD,kBAAc,QAAQ,OAAO,WAAW;EAC1C,CAAC;AAED,KAAG,qBAAqB,MAAK;AAC3B,WAAO,WAAW,EAAE,WAAU;AAC9B,WAAO,YAAY,YAAY,CAAC,EAAE,YAAW;EAC/C,CAAC;AAED,WAAS,eAAe,MAAK;AAC3B,OAAG,uCAAuC,MAAK;AAC7C,qBAAe,IAAI,gBACjB,GAAG;QACD,SAAS;OACV,CAAC;AAEJ,kBAAY,YAAY,SAAS,EAAE,UAAU;QAC3C,MAAM,CAAC,YAAW;AAChB,iBAAO,OAAO,EAAE,QAAQ,UAAU;QACpC;OACD;IACH,CAAC;AAED,OAAG,mCAAmC,MAAK;AACzC,YAAM,gBAAgB;QACpB,QAAQ;QACR,SAAS;;AAEX,qBAAe,IAAI,gBAAgB,WAAW,MAAM,aAAa,CAAC;AAClE,kBAAY,YAAY,SAAS,EAAE,UAAU;QAC3C,OAAO,CAAC,QAAO;AACb,iBAAO,GAAG,EAAE,KAAK,aAAa;QAChC;OACD;IACH,CAAC;EACH,CAAC;AACH,CAAC;", "names": []}