{"version": 3, "sources": ["src/main/angular/services/version.service.spec.ts"], "sourcesContent": ["import { describe, it, expect, vi, beforeEach } from \"vitest\";\r\nimport { provideZonelessChangeDetection } from \"@angular/core\";\r\nimport { TestBed } from \"@angular/core/testing\";\r\nimport { HttpClient } from \"@angular/common/http\";\r\nimport { of, throwError } from \"rxjs\";\r\nimport { VersionService } from \"./version.service\";\r\nimport { type Version } from \"../types/version.type\";\r\n\r\nvi.mock(\"../app.routes\", () => ({\r\n  backendUrl: () => \"http://localhost:8080\",\r\n}));\r\n\r\nvi.mock(\"../utils/log\", () => ({\r\n  tapLog: () => (source: any) => source,\r\n}));\r\n\r\ndescribe(\"VersionService\", () => {\r\n  let service: VersionService;\r\n  let httpClientMock: any;\r\n\r\n  const mockVersion: Version = {\r\n    version: \"1.2.0\",\r\n  };\r\n\r\n  beforeEach(() => {\r\n    // Create a mock HttpClient\r\n    httpClientMock = {\r\n      get: vi.fn(),\r\n    };\r\n\r\n    // Configure TestBed\r\n    TestBed.configureTestingModule({\r\n      providers: [\r\n        VersionService,\r\n        { provide: HttpClient, useValue: httpClientMock },\r\n        provideZonelessChangeDetection()\r\n      ],\r\n    });\r\n\r\n    service = TestBed.inject(VersionService);\r\n  });\r\n\r\n  it(\"should be created\", () => {\r\n    expect(service).toBeTruthy();\r\n    expect(service[\"httpClient\"]).toBeDefined();\r\n  });\r\n\r\n  describe(\"loadVersion\", () => {\r\n    it(\"should load version successfully\", (done) => {\r\n      httpClientMock.get.mockReturnValue(of(mockVersion));\r\n      service.loadVersion().subscribe({\r\n        next: (body) => {\r\n          // Assert\r\n          expect(httpClientMock.get).toHaveBeenCalledWith(\r\n            \"http://localhost:8080/version\"\r\n          );\r\n          expect(body).toEqual(mockVersion);\r\n        },\r\n      });\r\n    });\r\n\r\n    it(\"should handle errors gracefully\", (done) => {\r\n      const notFoundError = {\r\n        status: 404,\r\n        message: \"Not Found\",\r\n      };\r\n      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));\r\n      service.loadVersion().subscribe({\r\n        error: (err) => {\r\n          expect(httpClientMock.get).toHaveBeenCalledWith(\r\n            \"http://localhost:8080/version\"\r\n          );\r\n          expect(err).toBe(notFoundError);\r\n        },\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,UAAU,IAAI,QAAQ,IAAI,kBAAkB;AAQrD,GAAG,KAAK,iBAAiB,OAAO;EAC9B,YAAY,MAAM;EAClB;AAEF,GAAG,KAAK,gBAAgB,OAAO;EAC7B,QAAQ,MAAM,CAAC,WAAgB;EAC/B;AAEF,SAAS,kBAAkB,MAAK;AAC9B,MAAI;AACJ,MAAI;AAEJ,QAAM,cAAuB;IAC3B,SAAS;;AAGX,aAAW,MAAK;AAEd,qBAAiB;MACf,KAAK,GAAG,GAAE;;AAIZ,YAAQ,uBAAuB;MAC7B,WAAW;QACT;QACA,EAAE,SAAS,YAAY,UAAU,eAAc;QAC/C,+BAA8B;;KAEjC;AAED,cAAU,QAAQ,OAAO,cAAc;EACzC,CAAC;AAED,KAAG,qBAAqB,MAAK;AAC3B,WAAO,OAAO,EAAE,WAAU;AAC1B,WAAO,QAAQ,YAAY,CAAC,EAAE,YAAW;EAC3C,CAAC;AAED,WAAS,eAAe,MAAK;AAC3B,OAAG,oCAAoC,CAAC,SAAQ;AAC9C,qBAAe,IAAI,gBAAgB,GAAG,WAAW,CAAC;AAClD,cAAQ,YAAW,EAAG,UAAU;QAC9B,MAAM,CAAC,SAAQ;AAEb,iBAAO,eAAe,GAAG,EAAE,qBACzB,+BAA+B;AAEjC,iBAAO,IAAI,EAAE,QAAQ,WAAW;QAClC;OACD;IACH,CAAC;AAED,OAAG,mCAAmC,CAAC,SAAQ;AAC7C,YAAM,gBAAgB;QACpB,QAAQ;QACR,SAAS;;AAEX,qBAAe,IAAI,gBAAgB,WAAW,MAAM,aAAa,CAAC;AAClE,cAAQ,YAAW,EAAG,UAAU;QAC9B,OAAO,CAAC,QAAO;AACb,iBAAO,eAAe,GAAG,EAAE,qBACzB,+BAA+B;AAEjC,iBAAO,GAAG,EAAE,KAAK,aAAa;QAChC;OACD;IACH,CAAC;EACH,CAAC;AACH,CAAC;", "names": []}