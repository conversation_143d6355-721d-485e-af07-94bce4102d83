import {
  TestBed
} from "./chunk-JBEFZW42.js";
import {
  EnumService
} from "./chunk-YCFBNSZD.js";
import {
  HttpClient
} from "./chunk-TBX72RUN.js";
import "./chunk-PRSR237C.js";
import {
  of,
  provideZonelessChangeDetection,
  throwError
} from "./chunk-SG4F3HSD.js";

// src/main/angular/services/enum.service.spec.ts
import { describe, it, expect, vi, beforeEach } from "vitest";
var ALLSPECIES = [
  {
    code: 1,
    name: "Dog",
    text: "A dog is an animal of the species Canis lupus familiaris."
  },
  {
    code: 2,
    name: "Cat",
    text: "A cat is an animal of the species Felis catus."
  }
];
describe("EnumService", () => {
  let enumService;
  let httpClientMock;
  beforeEach(() => {
    httpClientMock = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn()
    };
    TestBed.configureTestingModule({
      providers: [
        EnumService,
        { provide: HttpClient, useValue: httpClientMock },
        provideZonelessChangeDetection()
      ]
    });
    enumService = TestBed.inject(EnumService);
  });
  it("should be created", () => {
    expect(enumService).toBeTruthy();
    expect(enumService["httpClient"]).toBeDefined();
  });
  describe("loadAllEnum", () => {
    it("should load enum items successfully", () => {
      httpClientMock.get.mockReturnValue(of({
        content: ALLSPECIES
      }));
      enumService.loadAllEnum("species").subscribe({
        next: (allItem) => {
          expect(allItem).toEqual(ALLSPECIES);
        }
      });
    });
    it("should handle errors gracefully", () => {
      const notFoundError = {
        status: 404,
        message: "Not Found"
      };
      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));
      enumService.loadAllEnum("species").subscribe({
        error: (err) => {
          expect(err).toBe(notFoundError);
        }
      });
    });
  });
  describe("createEnum", () => {
    it("should create enum item successfully", () => {
      const newItem = ALLSPECIES[0];
      httpClientMock.post.mockReturnValue(of(newItem));
      enumService.createEnum("species", newItem).subscribe({
        next: (createdItem) => {
          expect(createdItem).toEqual(newItem);
        }
      });
    });
    it("should handle errors gracefully", () => {
      const notFoundError = {
        status: 404,
        message: "Not Found"
      };
      httpClientMock.post.mockReturnValue(throwError(() => notFoundError));
      enumService.createEnum("species", ALLSPECIES[0]).subscribe({
        error: (err) => {
          expect(err).toBe(notFoundError);
        }
      });
    });
  });
});
//# sourceMappingURL=spec-enum.service.spec.js.map
