:restdocDir: ../../../../../../build/generated-snippets
= Pet-API

REST-API für die Verwaltung von _Pet_-Objekten.

== `POST /api/pet`

Die Operation speichert ein neues _Pet_-Objekt.

****

.CURL
include::{restdocDir}/post-api-pet/curl-request.adoc[]

.Request
include::{restdocDir}/post-api-pet/http-request.adoc[]

.Response
include::{restdocDir}/post-api-pet/response-body.adoc[]

****

Die Operation meldet `Created` bzw. den Code 201, wenn das Objekt erfolgreich angelegt wurde.

Die Operation meldet `Bad Request` bzw. den Code 400, wenn die Daten nicht verarbeitet wurden.

Die Operation meldet `Conflict` bzw. den Code 409, wenn die Daten bereits existieren, nicht vollständig oder gültig sind.

== `PUT /api/pet/{id}`

Die Operation aktualisiert ein existierendes _Pet_-Objekt oder erzeugt ein Neues.

****

.CURL
include::{restdocDir}/put-api-pet/curl-request.adoc[]

.Request
include::{restdocDir}/put-api-pet/http-request.adoc[]

.Response
include::{restdocDir}/put-api-pet/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn das Objekt erfolgreich aktualisiert wurde.

Die Operation meldet `Created` bzw. den Code 201, wenn das Objekt erfolgreich angelegt wurde.

Die Operation meldet `Bad Request` bzw. den Code 400, wenn die Daten nicht verarbeitet wurden.

Die Operation meldet `Conflict` bzw. den Code 409, wenn die Daten nicht vollständig oder gültig sind.

== `PATCH /api/pet/{id}`

Die Operation aktualisiert ein existierendes _Pet_-Objekt.

****

.CURL
include::{restdocDir}/patch-api-pet-name/curl-request.adoc[]

.Request
include::{restdocDir}/patch-api-pet-name/http-request.adoc[]

.Response
include::{restdocDir}/patch-api-pet-name/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn das Objekt erfolgreich aktualisiert wurde.

Die Operation meldet `Bad Request` bzw. den Code 400, wenn die Daten nicht verarbeitet wurden.

Die Operation meldet `Conflict` bzw. den Code 409, wenn die Daten nicht vollständig oder gültig sind.

== `GET /api/pet`

Die Operation gibt alle gespeicherten _Pet_-Objekte zurück.

****

.CURL
include::{restdocDir}/get-api-pet/curl-request.adoc[]

.Request
include::{restdocDir}/get-api-pet/http-request.adoc[]

.Response
include::{restdocDir}/get-api-pet/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn die Daten geladen wurden.

== `GET /api/pet/{id}`

Die Operation zeigt ein gespeichertes _Pet_-Objekt an.

****

.CURL
include::{restdocDir}/get-api-pet-by-id/curl-request.adoc[]

.Request
include::{restdocDir}/get-api-pet-by-id/http-request.adoc[]

.Response
include::{restdocDir}/get-api-pet-by-id/response-body.adoc[]

****

Die Operation meldet `Ok` bzw. den Code 200, wenn die Daten geladen wurden.

Die Operation meldet `Not found` bzw. den Code 404, wenn die Daten nicht existieren.

== `DELETE /api/pet/{id}`

Die Operation löscht ein gespeichertes _Pet_-Objekt.

****

.CURL
include::{restdocDir}/delete-api-pet/curl-request.adoc[]

.Request
include::{restdocDir}/delete-api-pet/http-request.adoc[]

****

Die Operation meldet `No Content` bzw. den Code 204, wenn die Daten gelöscht wurden.

Die Operation meldet `Not found` bzw. den Code 404, wenn die Daten nicht existieren.
