import {
  Component,
  DestroyRef,
  OnInit,
  inject,
  input,
  model,
  output,
} from "@angular/core";
import { CommonModule } from "@angular/common";
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { VisitService } from "../../../services/visit.service";
import { type VetItem } from "../../../types/vet.type";
import { type Visit } from "../../../types/visit.type";

@Component({
  selector: "app-visit-diagnose",
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: "./visit-diagnose.html",
  styles: ``,
})
export class VisitDiagnoseComponent implements OnInit {
  private destroyRef = inject(DestroyRef);
  private visitService = inject(VisitService);
  mode = input.required<"create" | "update">();
  visible = model.required<boolean>();
  allVetItem = input.required<VetItem[]>();
  visit = input.required<Visit>();
  form = new FormGroup({
    text: new FormControl("", Validators.required),
    vetItem: new FormControl<VetItem>(
      { value: "", text: "" },
      Validators.required
    ),
  });

  ngOnInit() {
    this.form.patchValue(this.visit());
    console.log(["ngOnInit", this.visit(), this.form.value]);
  }

  compareVetItems(item1: VetItem | null, item2: VetItem | null): boolean {
    return item1?.value === item2?.value;
  }

  get isSubmittable() {
    return this.form.dirty && this.form.valid;
  }

  cancelEmitter = output<Visit>({ alias: "cancel" });
  onCancelClicked() {
    this.cancelEmitter.emit(this.visit());
    this.visible.set(false);
    this.form.reset();
  }

  createEmitter = output<Visit>({ alias: "create" });
  updateEmitter = output<Visit>({ alias: "update" });
  onSubmitClicked() {
    if (this.mode() === "create") {
      const subscription = this.visitService
        .createVisit({
          ...this.visit(),
          text: this.form.value.text!,
          vet: ["api", "vet", this.form.value.vetItem!.value].join("/"),
        })
        .subscribe({
          next: (value) => {
            this.createEmitter.emit(value);
            this.visible.set(false);
            this.form.reset();
          },
        });
      this.destroyRef.onDestroy(() => {
        subscription.unsubscribe();
      });
    } else {
      const subscription = this.visitService
        .updateVisit({
          ...this.visit(),
          text: this.form.value.text!,
          vet: ["api", "vet", this.form.value.vetItem!.value].join("/"),
        })
        .subscribe({
          next: (value) => {
            this.updateEmitter.emit(value);
            this.visible.set(false);
            this.form.reset();
          },
        });
      this.destroyRef.onDestroy(() => {
        subscription.unsubscribe();
      });
    }
  }
}
