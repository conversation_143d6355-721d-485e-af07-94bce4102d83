{"version": 3, "sources": ["src/main/angular/services/version.service.spec.ts"], "sourcesContent": ["import { describe, it, expect, vi, beforeEach } from \"vitest\";\r\nimport { provideZonelessChangeDetection } from \"@angular/core\";\r\nimport { TestBed } from \"@angular/core/testing\";\r\nimport { HttpClient } from \"@angular/common/http\";\r\nimport { of, throwError } from \"rxjs\";\r\nimport { VersionService } from \"./version.service\";\r\nimport { type Version } from \"../types/version.type\";\r\n\r\nconst VERSION: Version = {\r\n  version: \"1.2.0\",\r\n};\r\n\r\ndescribe(\"VersionService\", () => {\r\n  let versionService: VersionService;\r\n  let httpClientMock: any;\r\n\r\n  beforeEach(() => {\r\n    httpClientMock = {\r\n      get: vi.fn(),\r\n    };\r\n    TestBed.configureTestingModule({\r\n      providers: [\r\n        VersionService,\r\n        { provide: HttpClient, useValue: httpClientMock },\r\n        provideZonelessChangeDetection(),\r\n      ],\r\n    });\r\n    versionService = TestBed.inject(VersionService);\r\n  });\r\n\r\n  it(\"should be created\", () => {\r\n    expect(versionService).toBeTruthy();\r\n    expect(versionService[\"httpClient\"]).toBeDefined();\r\n  });\r\n\r\n  describe(\"loadVersion\", () => {\r\n    it(\"should load version successfully\", (done) => {\r\n      httpClientMock.get.mockReturnValue(of(VERSION));\r\n      versionService.loadVersion().subscribe({\r\n        next: (body) => {\r\n          expect(httpClientMock.get).toHaveBeenCalledWith(\r\n            \"http://localhost:8080/version\"\r\n          );\r\n          expect(body).toEqual(VERSION);\r\n        },\r\n      });\r\n    });\r\n\r\n    it(\"should handle errors gracefully\", (done) => {\r\n      const notFoundError = {\r\n        status: 404,\r\n        message: \"Not Found\",\r\n      };\r\n      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));\r\n      versionService.loadVersion().subscribe({\r\n        error: (err) => {\r\n          expect(httpClientMock.get).toHaveBeenCalledWith(\r\n            \"http://localhost:8080/version\"\r\n          );\r\n          expect(err).toBe(notFoundError);\r\n        },\r\n      });\r\n    });\r\n  });\r\n});\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,UAAU,IAAI,QAAQ,IAAI,kBAAkB;AAQrD,IAAM,UAAmB;EACvB,SAAS;;AAGX,SAAS,kBAAkB,MAAK;AAC9B,MAAI;AACJ,MAAI;AAEJ,aAAW,MAAK;AACd,qBAAiB;MACf,KAAK,GAAG,GAAE;;AAEZ,YAAQ,uBAAuB;MAC7B,WAAW;QACT;QACA,EAAE,SAAS,YAAY,UAAU,eAAc;QAC/C,+BAA8B;;KAEjC;AACD,qBAAiB,QAAQ,OAAO,cAAc;EAChD,CAAC;AAED,KAAG,qBAAqB,MAAK;AAC3B,WAAO,cAAc,EAAE,WAAU;AACjC,WAAO,eAAe,YAAY,CAAC,EAAE,YAAW;EAClD,CAAC;AAED,WAAS,eAAe,MAAK;AAC3B,OAAG,oCAAoC,CAAC,SAAQ;AAC9C,qBAAe,IAAI,gBAAgB,GAAG,OAAO,CAAC;AAC9C,qBAAe,YAAW,EAAG,UAAU;QACrC,MAAM,CAAC,SAAQ;AACb,iBAAO,eAAe,GAAG,EAAE,qBACzB,+BAA+B;AAEjC,iBAAO,IAAI,EAAE,QAAQ,OAAO;QAC9B;OACD;IACH,CAAC;AAED,OAAG,mCAAmC,CAAC,SAAQ;AAC7C,YAAM,gBAAgB;QACpB,QAAQ;QACR,SAAS;;AAEX,qBAAe,IAAI,gBAAgB,WAAW,MAAM,aAAa,CAAC;AAClE,qBAAe,YAAW,EAAG,UAAU;QACrC,OAAO,CAAC,QAAO;AACb,iBAAO,eAAe,GAAG,EAAE,qBACzB,+BAA+B;AAEjC,iBAAO,GAAG,EAAE,KAAK,aAAa;QAChC;OACD;IACH,CAAC;EACH,CAAC;AACH,CAAC;", "names": []}