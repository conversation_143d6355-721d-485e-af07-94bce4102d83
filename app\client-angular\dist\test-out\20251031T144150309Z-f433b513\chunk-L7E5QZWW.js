import {
  DefaultValueAccessor,
  FormControl,
  FormControlName,
  FormGroup,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-K5TBLY2U.js";
import {
  HttpClient,
  HttpParams,
  backendUrl,
  tapLog
} from "./chunk-2BWCZPUI.js";
import {
  CommonModule
} from "./chunk-ZMC4WIII.js";
import "./chunk-PRSR237C.js";
import {
  Component,
  DestroyRef,
  Injectable,
  Input,
  Output,
  __spreadProps,
  __spreadValues,
  computed,
  inject,
  input,
  map,
  model,
  output,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵconditionalCreate,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtext,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-SG4F3HSD.js";

// src/main/angular/services/owner.service.ts
var OwnerService = class _OwnerService {
  httpClient = inject(HttpClient);
  loadAllOwner(params = void 0) {
    const path = [backendUrl(), "api", "owner"].join("/");
    return this.httpClient.get(path, { params }).pipe(tapLog("GET", path), map((body) => body.content));
  }
  createOwner(value) {
    const path = [backendUrl(), "api", "owner"].join("/");
    return this.httpClient.post(path, value).pipe(tapLog("POST", path));
  }
  updateOwner(value) {
    const path = [backendUrl(), "api", "owner", value.id].join("/");
    return this.httpClient.put(path, value).pipe(tapLog("PUT", path));
  }
  removeOwner(id) {
    const path = [backendUrl(), "api", "owner", id].join("/");
    return this.httpClient.delete(path).pipe(tapLog("DELETE", path));
  }
  static \u0275fac = function OwnerService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _OwnerService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _OwnerService, factory: _OwnerService.\u0275fac });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OwnerService, [{
    type: Injectable
  }], null, null);
})();

// src/main/angular/pages/clinic/owner-editor/owner-editor.ts
var OwnerEditorComponent = class _OwnerEditorComponent {
  destroyRef = inject(DestroyRef);
  restApi = inject(OwnerService);
  mode = input.required(...ngDevMode ? [{ debugName: "mode" }] : []);
  visible = model.required(...ngDevMode ? [{ debugName: "visible" }] : []);
  owner = input.required(...ngDevMode ? [{ debugName: "owner" }] : []);
  form = new FormGroup({
    name: new FormControl("", Validators.required),
    address: new FormControl("", Validators.required),
    contact: new FormControl("", Validators.required)
  });
  ngOnInit() {
    this.form.patchValue(this.owner());
  }
  get isSubmittable() {
    return this.form.dirty && this.form.valid;
  }
  cancelEmitter = output({ alias: "cancel" });
  onCancelClicked() {
    this.cancelEmitter.emit(this.owner());
    this.visible.set(false);
    this.form.reset();
  }
  createEmitter = output({ alias: "create" });
  updateEmitter = output({ alias: "update" });
  onSubmitClicked() {
    if (this.mode() === "create") {
      const subscription = this.restApi.createOwner({
        id: void 0,
        version: 0,
        allPetItem: [],
        name: this.form.value.name,
        address: this.form.value.address,
        contact: this.form.value.contact
      }).subscribe({
        next: (item) => {
          this.createEmitter.emit(item);
          this.visible.set(false);
          this.form.reset();
        }
      });
      this.destroyRef.onDestroy(() => {
        subscription.unsubscribe();
      });
    } else {
      const subscription = this.restApi.updateOwner(__spreadProps(__spreadValues({}, this.owner()), {
        name: this.form.value.name,
        address: this.form.value.address,
        contact: this.form.value.contact
      })).subscribe({
        next: (item) => {
          this.updateEmitter.emit(item);
          this.visible.set(false);
          this.form.reset();
        }
      });
      this.destroyRef.onDestroy(() => {
        subscription.unsubscribe();
      });
    }
  }
  static \u0275fac = function OwnerEditorComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _OwnerEditorComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _OwnerEditorComponent, selectors: [["app-owner-editor"]], inputs: { mode: [1, "mode"], visible: [1, "visible"], owner: [1, "owner"] }, outputs: { visible: "visibleChange", cancelEmitter: "cancel", createEmitter: "create", updateEmitter: "update" }, decls: 22, vars: 2, consts: [[3, "ngSubmit", "formGroup"], [1, "flex", "flex-col", "gap-2", "pt-4"], [1, "w-full"], [1, "floating-label"], [1, "label"], ["aria-label", "Name", "type", "text", "placeholder", "Enter a name", "formControlName", "name", 1, "input", "input-bordered", "w-full"], ["aria-label", "Address", "type", "text", "placeholder", "Enter an address", "formControlName", "address", 1, "input", "input-bordered", "w-full"], ["aria-label", "Contact", "type", "text", "placeholder", "Enter a contact", "formControlName", "contact", 1, "input", "input-bordered", "w-full"], [1, "join", "py-4"], ["type", "submit", 1, "btn", "join-item", 3, "disabled"], ["type", "button", 1, "btn", "join-item", 3, "click"]], template: function OwnerEditorComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "form", 0);
      \u0275\u0275listener("ngSubmit", function OwnerEditorComponent_Template_form_ngSubmit_0_listener() {
        return ctx.onSubmitClicked();
      });
      \u0275\u0275elementStart(1, "div", 1)(2, "div", 2)(3, "label", 3)(4, "span", 4);
      \u0275\u0275text(5, "Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(6, "input", 5);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "div", 2)(8, "label", 3)(9, "span", 4);
      \u0275\u0275text(10, "Address");
      \u0275\u0275elementEnd();
      \u0275\u0275element(11, "input", 6);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(12, "div", 2)(13, "label", 3)(14, "span", 4);
      \u0275\u0275text(15, "Contact");
      \u0275\u0275elementEnd();
      \u0275\u0275element(16, "input", 7);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(17, "div", 8)(18, "button", 9);
      \u0275\u0275text(19, " Ok ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "button", 10);
      \u0275\u0275listener("click", function OwnerEditorComponent_Template_button_click_20_listener() {
        return ctx.onCancelClicked();
      });
      \u0275\u0275text(21, " Cancel ");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275property("formGroup", ctx.form);
      \u0275\u0275advance(18);
      \u0275\u0275property("disabled", !ctx.isSubmittable);
    }
  }, dependencies: [ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OwnerEditorComponent, [{
    type: Component,
    args: [{ selector: "app-owner-editor", imports: [ReactiveFormsModule], template: '<form [formGroup]="form" (ngSubmit)="onSubmitClicked()">\r\n  <div class="flex flex-col gap-2 pt-4">\r\n    <div class="w-full">\r\n      <label class="floating-label">\r\n        <span class="label">Name</span>\r\n        <input\r\n          aria-label="Name"\r\n          type="text"\r\n          class="input input-bordered w-full"\r\n          placeholder="Enter a name"\r\n          formControlName="name"\r\n        />\r\n      </label>\r\n    </div>\r\n    <div class="w-full">\r\n      <label class="floating-label">\r\n        <span class="label">Address</span>\r\n        <input\r\n          aria-label="Address"\r\n          type="text"\r\n          class="input input-bordered w-full"\r\n          placeholder="Enter an address"\r\n          formControlName="address"\r\n        />\r\n      </label>\r\n    </div>\r\n    <div class="w-full">\r\n      <label class="floating-label">\r\n        <span class="label">Contact</span>\r\n        <input\r\n          aria-label="Contact"\r\n          type="text"\r\n          class="input input-bordered w-full"\r\n          placeholder="Enter a contact"\r\n          formControlName="contact"\r\n        />\r\n      </label>\r\n    </div>\r\n  </div>\r\n  <div class="join py-4">\r\n    <button type="submit" class="btn join-item" [disabled]="!isSubmittable">\r\n      Ok\r\n    </button>\r\n    <button type="button" class="btn join-item" (click)="onCancelClicked()">\r\n      Cancel\r\n    </button>\r\n  </div>\r\n</form>\r\n' }]
  }], null, { mode: [{ type: Input, args: [{ isSignal: true, alias: "mode", required: true }] }], visible: [{ type: Input, args: [{ isSignal: true, alias: "visible", required: true }] }, { type: Output, args: ["visibleChange"] }], owner: [{ type: Input, args: [{ isSignal: true, alias: "owner", required: true }] }], cancelEmitter: [{ type: Output, args: ["cancel"] }], createEmitter: [{ type: Output, args: ["create"] }], updateEmitter: [{ type: Output, args: ["update"] }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(OwnerEditorComponent, { className: "OwnerEditorComponent", filePath: "src/main/angular/pages/clinic/owner-editor/owner-editor.ts", lineNumber: 25 });
})();

// src/main/angular/pages/clinic/owner-lister/owner-lister.ts
var _forTrack0 = ($index, $item) => $item.id;
var _forTrack1 = ($index, $item) => $item.value;
function OwnerListerComponent_Conditional_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275element(1, "span", 10);
    \u0275\u0275elementEnd();
  }
}
function OwnerListerComponent_Conditional_13_Conditional_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr")(1, "td", 18)(2, "app-owner-editor", 19);
    \u0275\u0275listener("create", function OwnerListerComponent_Conditional_13_Conditional_14_Template_app_owner_editor_create_2_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.afterCreateItem($event));
    });
    \u0275\u0275twoWayListener("visibleChange", function OwnerListerComponent_Conditional_13_Conditional_14_Template_app_owner_editor_visibleChange_2_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      \u0275\u0275twoWayBindingSet(ctx_r1.ownerEditorCreate, $event) || (ctx_r1.ownerEditorCreate = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275twoWayProperty("visible", ctx_r1.ownerEditorCreate);
    \u0275\u0275property("owner", ctx_r1.newOwner());
  }
}
function OwnerListerComponent_Conditional_13_For_16_For_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const petItem_r6 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", petItem_r6.text, " ");
  }
}
function OwnerListerComponent_Conditional_13_For_16_ForEmpty_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "No pets");
    \u0275\u0275elementEnd();
  }
}
function OwnerListerComponent_Conditional_13_For_16_Conditional_23_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr")(1, "td", 18)(2, "span", 31);
    \u0275\u0275text(3, "Operation not implmented ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 32);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_For_16_Conditional_23_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.visitLister.set(false));
    });
    \u0275\u0275text(5, " Close ");
    \u0275\u0275elementEnd()()();
  }
}
function OwnerListerComponent_Conditional_13_For_16_Conditional_24_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr")(1, "td", 18)(2, "span", 31);
    \u0275\u0275text(3, "Operation not implmented ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 32);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_For_16_Conditional_24_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.petEditorCreate.set(false));
    });
    \u0275\u0275text(5, " Close ");
    \u0275\u0275elementEnd()()();
  }
}
function OwnerListerComponent_Conditional_13_For_16_Conditional_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr")(1, "td", 18)(2, "app-owner-editor", 33);
    \u0275\u0275listener("update", function OwnerListerComponent_Conditional_13_For_16_Conditional_25_Template_app_owner_editor_update_2_listener($event) {
      \u0275\u0275restoreView(_r9);
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.afterUpdateItem($event));
    });
    \u0275\u0275twoWayListener("visibleChange", function OwnerListerComponent_Conditional_13_For_16_Conditional_25_Template_app_owner_editor_visibleChange_2_listener($event) {
      \u0275\u0275restoreView(_r9);
      const ctx_r1 = \u0275\u0275nextContext(3);
      \u0275\u0275twoWayBindingSet(ctx_r1.ownerEditorUpdate, $event) || (ctx_r1.ownerEditorUpdate = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const owner_r5 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275twoWayProperty("visible", ctx_r1.ownerEditorUpdate);
    \u0275\u0275property("owner", owner_r5);
  }
}
function OwnerListerComponent_Conditional_13_For_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 20);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_For_16_Template_tr_click_0_listener() {
      const owner_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onOwnerClicked(owner_r5));
    });
    \u0275\u0275elementStart(1, "td", 21)(2, "div", 22);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "td", 21)(5, "div", 23);
    \u0275\u0275repeaterCreate(6, OwnerListerComponent_Conditional_13_For_16_For_7_Template, 2, 1, "div", 24, _forTrack1, false, OwnerListerComponent_Conditional_13_For_16_ForEmpty_8_Template, 2, 0, "span");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "td", 25)(10, "div", 26)(11, "button", 27);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_For_16_Template_button_click_11_listener() {
      const owner_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onVisitListerClicked(owner_r5));
    });
    \u0275\u0275elementStart(12, "span", 7);
    \u0275\u0275text(13, "list");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(14, "button", 28);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_For_16_Template_button_click_14_listener() {
      const owner_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onPetCreateEditorClicked(owner_r5));
    });
    \u0275\u0275elementStart(15, "span", 7);
    \u0275\u0275text(16, "pets");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "button", 29);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_For_16_Template_button_click_17_listener() {
      const owner_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onOwnerRemoveClicked(owner_r5));
    });
    \u0275\u0275elementStart(18, "span", 7);
    \u0275\u0275text(19, "delete");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(20, "button", 30);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_For_16_Template_button_click_20_listener() {
      const owner_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onOwnerEditorUpdateClicked(owner_r5));
    });
    \u0275\u0275elementStart(21, "span", 7);
    \u0275\u0275text(22, "edit");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275conditionalCreate(23, OwnerListerComponent_Conditional_13_For_16_Conditional_23_Template, 6, 0, "tr");
    \u0275\u0275conditionalCreate(24, OwnerListerComponent_Conditional_13_For_16_Conditional_24_Template, 6, 0, "tr");
    \u0275\u0275conditionalCreate(25, OwnerListerComponent_Conditional_13_For_16_Conditional_25_Template, 3, 2, "tr");
  }
  if (rf & 2) {
    const owner_r5 = ctx.$implicit;
    const $index_r10 = ctx.$index;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("border-l-2", ctx_r1.ownerId() === owner_r5.id)("bg-gray-100", $index_r10 % 2 === 1);
    \u0275\u0275property("title", owner_r5.name);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", owner_r5.name, " ");
    \u0275\u0275advance(3);
    \u0275\u0275repeater(owner_r5.allPetItem);
    \u0275\u0275advance(5);
    \u0275\u0275property("disabled", ctx_r1.ownerEditorDisabled());
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", ctx_r1.ownerEditorDisabled());
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", ctx_r1.ownerEditorDisabled());
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", ctx_r1.ownerEditorDisabled());
    \u0275\u0275advance(3);
    \u0275\u0275conditional(ctx_r1.visitLister() && ctx_r1.ownerId() === owner_r5.id ? 23 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.petEditorCreate() && ctx_r1.ownerId() === owner_r5.id ? 24 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.ownerEditorUpdate() && ctx_r1.ownerId() === owner_r5.id ? 25 : -1);
  }
}
function OwnerListerComponent_Conditional_13_ForEmpty_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "tr")(1, "td", 34);
    \u0275\u0275text(2, "No owners");
    \u0275\u0275elementEnd()();
  }
}
function OwnerListerComponent_Conditional_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "table", 9)(1, "thead", 11)(2, "tr", 12)(3, "th", 13)(4, "span", 14);
    \u0275\u0275text(5, "Name");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "th", 15)(7, "span", 14);
    \u0275\u0275text(8, "Pets");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "th", 16)(10, "button", 17);
    \u0275\u0275listener("click", function OwnerListerComponent_Conditional_13_Template_button_click_10_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onOwnerEditorCreateClicked());
    });
    \u0275\u0275elementStart(11, "span", 7);
    \u0275\u0275text(12, "add");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275elementStart(13, "tbody");
    \u0275\u0275conditionalCreate(14, OwnerListerComponent_Conditional_13_Conditional_14_Template, 3, 2, "tr");
    \u0275\u0275repeaterCreate(15, OwnerListerComponent_Conditional_13_For_16_Template, 26, 14, null, null, _forTrack0, false, OwnerListerComponent_Conditional_13_ForEmpty_17_Template, 3, 0, "tr");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(10);
    \u0275\u0275property("disabled", ctx_r1.ownerEditorDisabled());
    \u0275\u0275advance(4);
    \u0275\u0275conditional(ctx_r1.ownerEditorCreate() ? 14 : -1);
    \u0275\u0275advance();
    \u0275\u0275repeater(ctx_r1.allOwner());
  }
}
var OwnerListerComponent = class _OwnerListerComponent {
  destroyRef = inject(DestroyRef);
  restApi = inject(OwnerService);
  loading = signal(false, ...ngDevMode ? [{ debugName: "loading" }] : []);
  filterForm = new FormGroup({
    criteria: new FormControl("", Validators.required)
  });
  allOwner = signal([], ...ngDevMode ? [{ debugName: "allOwner" }] : []);
  afterCreateItem(newOwner) {
    this.allOwner.update((allOwner) => {
      return [newOwner, ...allOwner];
    });
  }
  afterUpdateItem(newOwner) {
    this.allOwner.update((allOwner) => {
      return allOwner.map((owner) => owner.id === newOwner.id ? newOwner : owner);
    });
  }
  afterRemoveItem(newOwner) {
    this.allOwner.update((allOwner) => {
      return allOwner.filter((owner) => owner.id !== newOwner.id);
    });
  }
  newOwner = computed(() => {
    return {
      version: 0,
      name: "",
      address: "",
      contact: "",
      allPetItem: []
    };
  }, ...ngDevMode ? [{ debugName: "newOwner" }] : []);
  ngOnInit() {
    this.onFilterClicked();
  }
  onFilterClicked() {
    this.loading.set(true);
    const params = new HttpParams().set("sort", "name,asc").set("name", this.filterForm.value.criteria);
    const subscription = this.restApi.loadAllOwner(params).subscribe({
      next: (allOwner) => {
        this.allOwner.set(allOwner);
      },
      complete: () => {
        this.loading.set(false);
      }
    });
    this.destroyRef.onDestroy(() => {
      subscription.unsubscribe();
    });
  }
  ownerId = signal(void 0, ...ngDevMode ? [{ debugName: "ownerId" }] : []);
  // no owner selected
  onOwnerClicked(owner) {
    this.ownerId.set(owner.id);
  }
  ownerEditorCreate = signal(false, ...ngDevMode ? [{ debugName: "ownerEditorCreate" }] : []);
  onOwnerEditorCreateClicked() {
    this.ownerId.set(void 0);
    this.ownerEditorCreate.set(true);
    this.ownerEditorUpdate.set(false);
    this.petEditorCreate.set(false);
    this.visitLister.set(false);
  }
  ownerEditorUpdate = signal(false, ...ngDevMode ? [{ debugName: "ownerEditorUpdate" }] : []);
  onOwnerEditorUpdateClicked(owner) {
    this.ownerId.set(owner.id);
    this.ownerEditorCreate.set(false);
    this.ownerEditorUpdate.set(true);
    this.petEditorCreate.set(false);
    this.visitLister.set(false);
  }
  petEditorCreate = signal(false, ...ngDevMode ? [{ debugName: "petEditorCreate" }] : []);
  onPetCreateEditorClicked(owner) {
    this.ownerId.set(owner.id);
    this.ownerEditorCreate.set(false);
    this.ownerEditorUpdate.set(false);
    this.petEditorCreate.set(true);
    this.visitLister.set(false);
  }
  visitLister = signal(false, ...ngDevMode ? [{ debugName: "visitLister" }] : []);
  onVisitListerClicked(owner) {
    this.ownerId.set(owner.id);
    this.ownerEditorCreate.set(false);
    this.ownerEditorUpdate.set(false);
    this.petEditorCreate.set(false);
    this.visitLister.set(!this.visitLister());
  }
  ownerEditorDisabled = computed(() => this.ownerEditorCreate() || this.ownerEditorUpdate() || this.petEditorCreate() || this.visitLister(), ...ngDevMode ? [{ debugName: "ownerEditorDisabled" }] : []);
  onOwnerRemoveClicked(owner) {
    this.ownerId.set(void 0);
    const text = owner.name;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Delete enum '" + hint + "' permanently?"))
      return;
    this.loading.set(true);
    const subscription = this.restApi.removeOwner(owner.id).subscribe({
      next: (owner2) => {
        this.afterRemoveItem(owner2);
      },
      complete: () => {
        this.loading.set(false);
      }
    });
    this.destroyRef.onDestroy(() => {
      subscription.unsubscribe();
    });
  }
  static \u0275fac = function OwnerListerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _OwnerListerComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _OwnerListerComponent, selectors: [["app-owner-lister"]], decls: 14, vars: 3, consts: [[1, "flex", "flex-col", "gap-1", "ml-2", "mr-2"], [3, "ngSubmit", "formGroup"], [1, "flex", "flex-row", "gap-2", "items-center", "pb-2", "pr-2"], [1, "floating-label", "w-full"], [1, "label"], ["aria-label", "Filter", "type", "text", "placeholder", "Enter filter critria", "formControlName", "criteria", 1, "input", "input-bordered", "w-full"], ["type", "submit", "title", "Filter items", 1, "btn", "btn-circle", "btn-outline", 3, "disabled"], [1, "material-icons"], [1, "h-screen", "flex", "justify-center", "items-start"], [1, "table-fixed"], [1, "loading", "loading-spinner", "loading-xl"], [1, "justify-between"], [1, "bg-gray-200"], [1, "px-2", "py-3", "text-left", "w-1/3", "table-cell"], [1, "text-gray-600"], [1, "px-2", "py-3", "text-left", "w-full", "table-cell"], [1, "px-2", "py-3", "text-right", "w-0", "table-cell"], ["title", "Add a new owner", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], ["colspan", "3", 1, "border-l-4", "px-2"], ["mode", "create", 3, "create", "visibleChange", "visible", "owner"], [3, "click", "title"], [1, "px-2", "py-3", "text-left", "table-cell"], [1, "text-sm", "underline", "text-blue-600"], [1, "flex", "flex-col", "text-sm"], [1, "underline", "text-blue-600"], [1, "px-2", "py-3", "table-cell"], [1, "grid", "grid-cols-1", "md:grid-cols-4", "items-center", "gap-1", "w-max"], ["title", "Show all visits", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], ["title", "Add a new pet", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], ["title", "Delete an owner", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], ["title", "Edit an owner", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], [1, "text-sm"], [1, "btn", 3, "click"], ["mode", "update", 3, "update", "visibleChange", "visible", "owner"], ["colspan", "3", 1, "px-2"]], template: function OwnerListerComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "h1");
      \u0275\u0275text(1, "Owner");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(2, "div", 0)(3, "form", 1);
      \u0275\u0275listener("ngSubmit", function OwnerListerComponent_Template_form_ngSubmit_3_listener() {
        return ctx.onFilterClicked();
      });
      \u0275\u0275elementStart(4, "div", 2)(5, "label", 3)(6, "span", 4);
      \u0275\u0275text(7, "Filter");
      \u0275\u0275elementEnd();
      \u0275\u0275element(8, "input", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "button", 6)(10, "span", 7);
      \u0275\u0275text(11, "search");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275conditionalCreate(12, OwnerListerComponent_Conditional_12_Template, 2, 0, "div", 8)(13, OwnerListerComponent_Conditional_13_Template, 18, 3, "table", 9);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(3);
      \u0275\u0275property("formGroup", ctx.filterForm);
      \u0275\u0275advance(6);
      \u0275\u0275property("disabled", ctx.ownerEditorDisabled());
      \u0275\u0275advance(3);
      \u0275\u0275conditional(ctx.loading() ? 12 : 13);
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, OwnerEditorComponent], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OwnerListerComponent, [{
    type: Component,
    args: [{ selector: "app-owner-lister", imports: [CommonModule, ReactiveFormsModule, OwnerEditorComponent], template: '<h1>Owner</h1>\r\n\r\n<div class="flex flex-col gap-1 ml-2 mr-2">\r\n  <form [formGroup]="filterForm" (ngSubmit)="onFilterClicked()">\r\n    <div class="flex flex-row gap-2 items-center pb-2 pr-2">\r\n      <label class="floating-label w-full">\r\n        <span class="label">Filter</span>\r\n        <input\r\n          aria-label="Filter"\r\n          type="text"\r\n          class="input input-bordered w-full"\r\n          placeholder="Enter filter critria"\r\n          formControlName="criteria"\r\n        />\r\n      </label>\r\n      <button\r\n        type="submit"\r\n        title="Filter items"\r\n        class="btn btn-circle btn-outline"\r\n        [disabled]="ownerEditorDisabled()"\r\n      >\r\n        <span class="material-icons">search</span>\r\n      </button>\r\n    </div>\r\n  </form>\r\n  @if (loading()) {\r\n    <div class="h-screen flex justify-center items-start">\r\n      <span class="loading loading-spinner loading-xl"></span>\r\n    </div>\r\n  } @else {\r\n    <table class="table-fixed">\r\n      <thead class="justify-between">\r\n        <tr class="bg-gray-200">\r\n          <th class="px-2 py-3 text-left w-1/3 table-cell">\r\n            <span class="text-gray-600">Name</span>\r\n          </th>\r\n          <th class="px-2 py-3 text-left w-full table-cell">\r\n            <span class="text-gray-600">Pets</span>\r\n          </th>\r\n          <th class="px-2 py-3 text-right w-0 table-cell">\r\n            <button\r\n              title="Add a new owner"\r\n              class="btn btn-circle btn-outline"\r\n              (click)="onOwnerEditorCreateClicked()"\r\n              [disabled]="ownerEditorDisabled()"\r\n            >\r\n              <span class="material-icons">add</span>\r\n            </button>\r\n          </th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        @if (ownerEditorCreate()) {\r\n          <tr>\r\n            <td class="border-l-4 px-2" colspan="3">\r\n              <app-owner-editor\r\n                mode="create"\r\n                (create)="afterCreateItem($event)"\r\n                [(visible)]="ownerEditorCreate"\r\n                [owner]="newOwner()"\r\n              />\r\n            </td>\r\n          </tr>\r\n        }\r\n        @for (owner of allOwner(); track owner.id) {\r\n          <tr\r\n            [title]="owner.name"\r\n            [class.border-l-2]="ownerId() === owner.id"\r\n            [class.bg-gray-100]="$index % 2 === 1"\r\n            (click)="onOwnerClicked(owner)"\r\n          >\r\n            <td class="px-2 py-3 text-left table-cell">\r\n              <div class="text-sm underline text-blue-600">\r\n                {{ owner.name }}\r\n              </div>\r\n            </td>\r\n            <td class="px-2 py-3 text-left table-cell">\r\n              <div class="flex flex-col text-sm">\r\n                @for (petItem of owner.allPetItem; track petItem.value) {\r\n                  <div class="underline text-blue-600">\r\n                    {{ petItem.text }}\r\n                  </div>\r\n                } @empty {\r\n                  <span>No pets</span>\r\n                }\r\n              </div>\r\n            </td>\r\n            <td class="px-2 py-3 table-cell">\r\n              <div\r\n                class="grid grid-cols-1 md:grid-cols-4 items-center gap-1 w-max"\r\n              >\r\n                <button\r\n                  title="Show all visits"\r\n                  class="btn btn-circle btn-outline"\r\n                  (click)="onVisitListerClicked(owner)"\r\n                  [disabled]="ownerEditorDisabled()"\r\n                >\r\n                  <span class="material-icons">list</span>\r\n                </button>\r\n                <button\r\n                  title="Add a new pet"\r\n                  class="btn btn-circle btn-outline"\r\n                  (click)="onPetCreateEditorClicked(owner)"\r\n                  [disabled]="ownerEditorDisabled()"\r\n                >\r\n                  <span class="material-icons">pets</span>\r\n                </button>\r\n                <button\r\n                  title="Delete an owner"\r\n                  class="btn btn-circle btn-outline"\r\n                  (click)="onOwnerRemoveClicked(owner)"\r\n                  [disabled]="ownerEditorDisabled()"\r\n                >\r\n                  <span class="material-icons">delete</span>\r\n                </button>\r\n                <button\r\n                  title="Edit an owner"\r\n                  class="btn btn-circle btn-outline"\r\n                  (click)="onOwnerEditorUpdateClicked(owner)"\r\n                  [disabled]="ownerEditorDisabled()"\r\n                >\r\n                  <span class="material-icons">edit</span>\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          @if (visitLister() && ownerId() === owner.id) {\r\n            <tr>\r\n              <td class="border-l-4 px-2" colspan="3">\r\n                <span class="text-sm">Operation not implmented </span>\r\n                <button class="btn" (click)="visitLister.set(false)">\r\n                  Close\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          }\r\n          @if (petEditorCreate() && ownerId() === owner.id) {\r\n            <tr>\r\n              <td class="border-l-4 px-2" colspan="3">\r\n                <span class="text-sm">Operation not implmented </span>\r\n                <button class="btn" (click)="petEditorCreate.set(false)">\r\n                  Close\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          }\r\n          @if (ownerEditorUpdate() && ownerId() === owner.id) {\r\n            <tr>\r\n              <td class="border-l-4 px-2" colspan="3">\r\n                <app-owner-editor\r\n                  mode="update"\r\n                  (update)="afterUpdateItem($event)"\r\n                  [(visible)]="ownerEditorUpdate"\r\n                  [owner]="owner"\r\n                />\r\n              </td>\r\n            </tr>\r\n          }\r\n        } @empty {\r\n          <tr>\r\n            <td class="px-2" colspan="3">No owners</td>\r\n          </tr>\r\n        }\r\n      </tbody>\r\n    </table>\r\n  }\r\n</div>\r\n' }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(OwnerListerComponent, { className: "OwnerListerComponent", filePath: "src/main/angular/pages/clinic/owner-lister/owner-lister.ts", lineNumber: 28 });
})();

// src/main/angular/pages/clinic/owner.routes.ts
var routes = [
  { path: "", component: OwnerListerComponent, providers: [OwnerService] }
];
export {
  routes
};
//# sourceMappingURL=chunk-L7E5QZWW.js.map
