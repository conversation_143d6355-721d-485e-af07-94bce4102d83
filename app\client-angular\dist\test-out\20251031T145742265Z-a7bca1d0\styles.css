/* src/main/angular/styles.css */
/*! tailwindcss v4.1.16 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root,
  :host {
    --font-sans:
      ui-sans-serif,
      system-ui,
      sans-serif,
      "Apple Color Emoji",
      "Segoe UI Emoji",
      "Segoe UI Symbol",
      "Noto Color Emoji";
    --font-mono:
      ui-monospace,
      SFMono-Regular,
      Menlo,
      Monaco,
      Consolas,
      "Liberation Mono",
      "Courier New",
      monospace;
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html,
  :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b,
  strong {
    font-weight: bolder;
  }
  code,
  kbd,
  samp,
  pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol,
  ul,
  menu {
    list-style: none;
  }
  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    display: block;
    vertical-align: middle;
  }
  img,
  video {
    max-width: 100%;
    height: auto;
  }
  button,
  input,
  select,
  optgroup,
  textarea,
  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button)) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
    }
    @supports (color: color-mix(in lab, red, red)) {
       {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit,
  ::-webkit-datetime-edit-year-field,
  ::-webkit-datetime-edit-month-field,
  ::-webkit-datetime-edit-day-field,
  ::-webkit-datetime-edit-hour-field,
  ::-webkit-datetime-edit-minute-field,
  ::-webkit-datetime-edit-second-field,
  ::-webkit-datetime-edit-millisecond-field,
  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  ::-webkit-calendar-picker-indicator {
    line-height: 1;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button,
  input:where([type=button], [type=reset], [type=submit]),
  ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button,
  ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden=until-found])) {
    display: none !important;
  }
}
@layer utilities {
  @layer daisyui.component {
    .floating-label {
      position: relative;
      display: block;
    }
    .floating-label input {
      display: block;
    }
    .floating-label input::placeholder {
      transition:
        top 0.1s ease-out,
        translate 0.1s ease-out,
        scale 0.1s ease-out,
        opacity 0.1s ease-out;
    }
    .floating-label textarea::placeholder {
      transition:
        top 0.1s ease-out,
        translate 0.1s ease-out,
        scale 0.1s ease-out,
        opacity 0.1s ease-out;
    }
    .floating-label > span {
      position: absolute;
      inset-inline-start: calc(0.25rem * 3);
      z-index: 1;
      background-color: var(--color-base-100);
      padding-inline: calc(0.25rem * 1);
      opacity: 0%;
      font-size: 0.875rem;
      top: calc(var(--size-field, 0.25rem) * 10 / 2);
      line-height: 1;
      border-radius: 2px;
      pointer-events: none;
      translate: 0 -50%;
      transition:
        top 0.1s ease-out,
        translate 0.1s ease-out,
        scale 0.1s ease-out,
        opacity 0.1s ease-out;
    }
    :is(.floating-label:focus-within, .floating-label:not(:has(input:placeholder-shown, textarea:placeholder-shown))) ::placeholder {
      opacity: 0%;
      top: 0;
      translate: -12.5% calc(-50% - 0.125em);
      scale: 0.75;
      pointer-events: auto;
    }
    :is(.floating-label:focus-within, .floating-label:not(:has(input:placeholder-shown, textarea:placeholder-shown))) > span {
      opacity: 100%;
      top: 0;
      translate: -12.5% calc(-50% - 0.125em);
      scale: 0.75;
      pointer-events: auto;
      z-index: 2;
    }
    .floating-label:has(:disabled, [disabled]) > span {
      opacity: 0%;
    }
    .floating-label:has(.input-xs, .select-xs, .textarea-xs) span {
      font-size: 0.6875rem;
      top: calc(var(--size-field, 0.25rem) * 6 / 2);
    }
    .floating-label:has(.input-sm, .select-sm, .textarea-sm) span {
      font-size: 0.75rem;
      top: calc(var(--size-field, 0.25rem) * 8 / 2);
    }
    .floating-label:has(.input-md, .select-md, .textarea-md) span {
      font-size: 0.875rem;
      top: calc(var(--size-field, 0.25rem) * 10 / 2);
    }
    .floating-label:has(.input-lg, .select-lg, .textarea-lg) span {
      font-size: 1.125rem;
      top: calc(var(--size-field, 0.25rem) * 12 / 2);
    }
    .floating-label:has(.input-xl, .select-xl, .textarea-xl) span {
      font-size: 1.375rem;
      top: calc(var(--size-field, 0.25rem) * 14 / 2);
    }
  }
  @layer daisyui.component {
    :where(.btn) {
      width: unset;
    }
  }
  .prose .btn {
    text-decoration-line: none;
  }
  @layer daisyui.component {
    .btn {
      display: inline-flex;
      flex-shrink: 0;
      cursor: pointer;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: center;
      gap: calc(0.25rem * 1.5);
      text-align: center;
      vertical-align: middle;
      outline-offset: 2px;
      webkit-user-select: none;
      -webkit-user-select: none;
      user-select: none;
      padding-inline: var(--btn-p);
      color: var(--btn-fg);
      --tw-prose-links: var(--btn-fg);
      height: var(--size);
      font-size: var(--fontsize, 0.875rem);
      font-weight: 600;
      outline-color: var(--btn-color, var(--color-base-content));
      transition-property:
        color,
        background-color,
        border-color,
        box-shadow;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
      transition-duration: 0.2s;
      border-start-start-radius: var(--join-ss, var(--radius-field));
      border-start-end-radius: var(--join-se, var(--radius-field));
      border-end-start-radius: var(--join-es, var(--radius-field));
      border-end-end-radius: var(--join-ee, var(--radius-field));
      background-color: var(--btn-bg);
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--btn-noise);
      border-width: var(--border);
      border-style: solid;
      border-color: var(--btn-border);
      text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
      touch-action: manipulation;
      box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
      --size: calc(var(--size-field, 0.25rem) * 10);
      --btn-bg: var(--btn-color, var(--color-base-200));
      --btn-fg: var(--color-base-content);
      --btn-p: 1rem;
      --btn-border: var(--btn-bg);
      --btn-shadow: 0 3px 2px -2px var(--btn-bg), 0 4px 3px -2px var(--btn-bg);
      --btn-noise: var(--fx-noise);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn {
        --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn {
        --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000), 0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
      }
    }
    @media (hover: hover) {
      .btn:hover {
        --btn-bg: var(--btn-color, var(--color-base-200));
      }
      @supports (color: color-mix(in lab, red, red)) {
        .btn:hover {
          --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
        }
      }
    }
    .btn:focus-visible,
    .btn:has(:focus-visible) {
      outline-width: 2px;
      outline-style: solid;
      isolation: isolate;
    }
    .btn:active:not(.btn-active) {
      translate: 0 0.5px;
      --btn-bg: var(--btn-color, var(--color-base-200));
      --btn-border: var(--btn-color, var(--color-base-200));
      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn:active:not(.btn-active) {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn:active:not(.btn-active) {
        --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
    }
    .btn:is(input[type=checkbox], input[type=radio]) {
      appearance: none;
    }
    .btn:is(input[type=checkbox], input[type=radio])::after {
      --tw-content: attr(aria-label);
      content: var(--tw-content);
    }
    .btn:where(input:checked:not(.filter .btn)) {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
      isolation: isolate;
    }
  }
  @layer daisyui.modifier {
    .btn:disabled {
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: var(--color-base-content);
    }
    .btn:disabled:not(.btn-link, .btn-ghost) {
      background-color: var(--color-base-content);
      box-shadow: none;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn:disabled:not(.btn-link, .btn-ghost) {
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn:disabled {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
    }
    @media (hover: hover) {
      .btn:disabled:hover {
        pointer-events: none;
        background-color: var(--color-neutral);
        --btn-border: #0000;
        --btn-fg: var(--color-base-content);
      }
      @supports (color: color-mix(in lab, red, red)) {
        .btn:disabled:hover {
          background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
        }
      }
      @supports (color: color-mix(in lab, red, red)) {
        .btn:disabled:hover {
          --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
        }
      }
    }
  }
  @layer daisyui.modifier {
    .btn[disabled] {
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: var(--color-base-content);
    }
    .btn[disabled]:not(.btn-link, .btn-ghost) {
      background-color: var(--color-base-content);
      box-shadow: none;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn[disabled]:not(.btn-link, .btn-ghost) {
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .btn[disabled] {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
    }
    @media (hover: hover) {
      .btn[disabled]:hover {
        pointer-events: none;
        background-color: var(--color-neutral);
        --btn-border: #0000;
        --btn-fg: var(--color-base-content);
      }
      @supports (color: color-mix(in lab, red, red)) {
        .btn[disabled]:hover {
          background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
        }
      }
      @supports (color: color-mix(in lab, red, red)) {
        .btn[disabled]:hover {
          --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
        }
      }
    }
  }
  @layer daisyui.component {
    .loading {
      pointer-events: none;
      display: inline-block;
      aspect-ratio: 1 / 1;
      background-color: currentcolor;
      vertical-align: middle;
      width: calc(var(--size-selector, 0.25rem) * 6);
      -webkit-mask-size: 100%;
      mask-size: 100%;
      -webkit-mask-repeat: no-repeat;
      mask-repeat: no-repeat;
      -webkit-mask-position: center;
      mask-position: center;
      -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
      mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
    }
  }
  .pointer-events-auto {
    pointer-events: auto;
  }
  .visible {
    visibility: visible;
  }
  @layer daisyui.component {
    .list {
      display: flex;
      flex-direction: column;
      font-size: 0.875rem;
    }
    .list .list-row {
      --list-grid-cols: minmax(0, auto) 1fr;
      position: relative;
      display: grid;
      grid-auto-flow: column;
      gap: calc(0.25rem * 4);
      border-radius: var(--radius-box);
      padding: calc(0.25rem * 4);
      word-break: break-word;
      grid-template-columns: var(--list-grid-cols);
    }
    :is(.list > :not(:last-child).list-row, .list > :not(:last-child) .list-row):after {
      content: "";
      border-bottom: var(--border) solid;
      inset-inline: var(--radius-box);
      position: absolute;
      bottom: calc(0.25rem * 0);
      border-color: var(--color-base-content);
    }
    @supports (color: color-mix(in lab, red, red)) {
       {
        border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
      }
    }
  }
  @layer daisyui.modifier {
    .list .list-row:has(.list-col-grow:nth-child(1)) {
      --list-grid-cols: 1fr;
    }
    .list .list-row:has(.list-col-grow:nth-child(2)) {
      --list-grid-cols: minmax(0, auto) 1fr;
    }
    .list .list-row:has(.list-col-grow:nth-child(3)) {
      --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
    }
    .list .list-row:has(.list-col-grow:nth-child(4)) {
      --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
    }
    .list .list-row:has(.list-col-grow:nth-child(5)) {
      --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
    }
    .list .list-row:has(.list-col-grow:nth-child(6)) {
      --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
    }
    .list .list-row :not(.list-col-wrap) {
      grid-row-start: 1;
    }
  }
  @layer daisyui.component {
    .input {
      cursor: text;
      border: var(--border) solid #0000;
      position: relative;
      display: inline-flex;
      flex-shrink: 1;
      appearance: none;
      align-items: center;
      gap: calc(0.25rem * 2);
      background-color: var(--color-base-100);
      padding-inline: calc(0.25rem * 3);
      vertical-align: middle;
      white-space: nowrap;
      width: clamp(3rem, 20rem, 100%);
      height: var(--size);
      font-size: max(var(--font-size, 0.875rem), 0.875rem);
      touch-action: manipulation;
      border-start-start-radius: var(--join-ss, var(--radius-field));
      border-start-end-radius: var(--join-se, var(--radius-field));
      border-end-start-radius: var(--join-es, var(--radius-field));
      border-end-end-radius: var(--join-ee, var(--radius-field));
      border-color: var(--input-color);
      box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      --size: calc(var(--size-field, 0.25rem) * 10);
      --input-color: var(--color-base-content);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .input {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .input {
        --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
    }
    .input:where(input) {
      display: inline-flex;
    }
    .input :where(input) {
      display: inline-flex;
      height: 100%;
      width: 100%;
      appearance: none;
      background-color: transparent;
      border: none;
    }
    .input :where(input):focus,
    .input :where(input):focus-within {
      --tw-outline-style: none;
      outline-style: none;
    }
    @media (forced-colors: active) {
      .input :where(input):focus,
      .input :where(input):focus-within {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    .input :where(input[type=url]),
    .input :where(input[type=email]) {
      direction: ltr;
    }
    .input :where(input[type=date]) {
      display: inline-flex;
    }
    .input:focus,
    .input:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .input:focus,
      .input:focus-within {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
    }
    @media (pointer: coarse) {
      @supports (-webkit-touch-callout: none) {
        .input:focus,
        .input:focus-within {
          --font-size: 1rem;
        }
      }
    }
    .input:has(> input[disabled]),
    .input:is(:disabled, [disabled]),
    fieldset:disabled .input {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      box-shadow: none;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .input:has(> input[disabled]),
      .input:is(:disabled, [disabled]),
      fieldset:disabled .input {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
    }
    :is(.input:has(> input[disabled]), .input:is(:disabled, [disabled]), fieldset:disabled .input)::placeholder {
      color: var(--color-base-content);
    }
    @supports (color: color-mix(in lab, red, red)) {
       {
        color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
    }
    .input:has(> input[disabled]) > input[disabled] {
      cursor: not-allowed;
    }
    .input::-webkit-date-and-time-value {
      text-align: inherit;
    }
    .input[type=number]::-webkit-inner-spin-button {
      margin-block: calc(0.25rem * -3);
      margin-inline-end: calc(0.25rem * -3);
    }
    .input::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
    }
    .input:has(> input[type=date]) :where(input[type=date]) {
      display: inline-flex;
      webkit-appearance: none;
      appearance: none;
    }
    .input:has(> input[type=date]) input[type=date]::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
      width: 1em;
      height: 1em;
      cursor: pointer;
    }
  }
  @layer daisyui.component {
    .table {
      font-size: 0.875rem;
      position: relative;
      width: 100%;
      border-radius: var(--radius-box);
      text-align: left;
    }
    .table:where(:dir(rtl), [dir=rtl], [dir=rtl] *) {
      text-align: right;
    }
    @media (hover: hover) {
      :is(.table tr.row-hover, .table tr.row-hover:nth-child(even)):hover {
        background-color: var(--color-base-200);
      }
    }
    .table :where(th, td) {
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 3);
      vertical-align: middle;
    }
    .table :where(thead, tfoot) {
      white-space: nowrap;
      color: var(--color-base-content);
      font-size: 0.875rem;
      font-weight: 600;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .table :where(thead, tfoot) {
        color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
      }
    }
    .table :where(tfoot) {
      border-top: var(--border) solid var(--color-base-content);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .table :where(tfoot) {
        border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
    .table :where(.table-pin-rows thead tr) {
      position: sticky;
      top: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    .table :where(.table-pin-rows tfoot tr) {
      position: sticky;
      bottom: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    .table :where(.table-pin-cols tr th) {
      position: sticky;
      right: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      background-color: var(--color-base-100);
    }
    .table :where(thead tr, tbody tr:not(:last-child)) {
      border-bottom: var(--border) solid var(--color-base-content);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .table :where(thead tr, tbody tr:not(:last-child)) {
        border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  @layer daisyui.component {
    .textarea {
      border: var(--border) solid #0000;
      min-height: calc(0.25rem * 20);
      flex-shrink: 1;
      appearance: none;
      border-radius: var(--radius-field);
      background-color: var(--color-base-100);
      padding-block: calc(0.25rem * 2);
      vertical-align: middle;
      width: clamp(3rem, 20rem, 100%);
      padding-inline-start: 0.75rem;
      padding-inline-end: 0.75rem;
      font-size: max(var(--font-size, 0.875rem), 0.875rem);
      touch-action: manipulation;
      border-color: var(--input-color);
      box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      --input-color: var(--color-base-content);
    }
    @supports (color: color-mix(in lab, red, red)) {
      .textarea {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .textarea {
        --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
    }
    .textarea textarea {
      appearance: none;
      background-color: transparent;
      border: none;
    }
    .textarea textarea:focus,
    .textarea textarea:focus-within {
      --tw-outline-style: none;
      outline-style: none;
    }
    @media (forced-colors: active) {
      .textarea textarea:focus,
      .textarea textarea:focus-within {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    .textarea:focus,
    .textarea:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .textarea:focus,
      .textarea:focus-within {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
    }
    @media (pointer: coarse) {
      @supports (-webkit-touch-callout: none) {
        .textarea:focus,
        .textarea:focus-within {
          --font-size: 1rem;
        }
      }
    }
    .textarea:has(> textarea[disabled]),
    .textarea:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      box-shadow: none;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .textarea:has(> textarea[disabled]),
      .textarea:is(:disabled, [disabled]) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
    }
    :is(.textarea:has(> textarea[disabled]), .textarea:is(:disabled, [disabled]))::placeholder {
      color: var(--color-base-content);
    }
    @supports (color: color-mix(in lab, red, red)) {
       {
        color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
    }
    .textarea:has(> textarea[disabled]) > textarea[disabled] {
      cursor: not-allowed;
    }
  }
  .z-10 {
    z-index: 10;
  }
  @layer daisyui.component {
    .filter {
      display: flex;
      flex-wrap: wrap;
    }
    .filter input[type=radio] {
      width: auto;
    }
    .filter input {
      overflow: hidden;
      opacity: 100%;
      scale: 1;
      transition:
        margin 0.1s,
        opacity 0.3s,
        padding 0.3s,
        border-width 0.1s;
    }
    .filter input:not(:last-child) {
      margin-inline-end: calc(0.25rem * 1);
    }
    .filter input.filter-reset {
      aspect-ratio: 1 / 1;
    }
    .filter input.filter-reset::after {
      --tw-content: "\d7";
      content: var(--tw-content);
    }
    .filter:not(:has(input:checked:not(.filter-reset))) .filter-reset,
    .filter:not(:has(input:checked:not(.filter-reset))) input[type=reset] {
      scale: 0;
      border-width: 0;
      margin-inline: calc(0.25rem * 0);
      width: calc(0.25rem * 0);
      padding-inline: calc(0.25rem * 0);
      opacity: 0%;
    }
    .filter:has(input:checked:not(.filter-reset)) input:not(:checked, .filter-reset, input[type=reset]) {
      scale: 0;
      border-width: 0;
      margin-inline: calc(0.25rem * 0);
      width: calc(0.25rem * 0);
      padding-inline: calc(0.25rem * 0);
      opacity: 0%;
    }
  }
  @layer daisyui.component {
    .label {
      display: inline-flex;
      align-items: center;
      gap: calc(0.25rem * 1.5);
      white-space: nowrap;
      color: currentcolor;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .label {
        color: color-mix(in oklab, currentcolor 60%, transparent);
      }
    }
    .label:has(input) {
      cursor: pointer;
    }
    .label:is(.input > *, .select > *) {
      display: flex;
      height: calc(100% - 0.5rem);
      align-items: center;
      padding-inline: calc(0.25rem * 3);
      white-space: nowrap;
      font-size: inherit;
    }
    .label:is(.input > *, .select > *):first-child {
      margin-inline-start: calc(0.25rem * -3);
      margin-inline-end: calc(0.25rem * 3);
      border-inline-end: var(--border) solid currentColor;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .label:is(.input > *, .select > *):first-child {
        border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
      }
    }
    .label:is(.input > *, .select > *):last-child {
      margin-inline-start: calc(0.25rem * 3);
      margin-inline-end: calc(0.25rem * -3);
      border-inline-start: var(--border) solid currentColor;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .label:is(.input > *, .select > *):last-child {
        border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
      }
    }
  }
  .join-item:where(*:not(:first-child, :disabled, [disabled], .btn-disabled)) {
    margin-inline-start: calc(var(--border, 1px) * -1);
    margin-block-start: 0;
  }
  .join-item:where(*:is(:disabled, [disabled], .btn-disabled)) {
    border-width: var(--border, 1px) 0 var(--border, 1px) var(--border, 1px);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  @layer daisyui.component {
    .status {
      display: inline-block;
      aspect-ratio: 1 / 1;
      width: calc(0.25rem * 2);
      height: calc(0.25rem * 2);
      border-radius: var(--radius-selector);
      background-color: var(--color-base-content);
      background-position: center;
      background-repeat: no-repeat;
      vertical-align: middle;
      color: color-mix(in srgb, #000 30%, transparent);
      background-image:
        radial-gradient(
          circle at 35% 30%,
          oklch(1 0 0 / calc(var(--depth) * 0.5)),
          #0000);
      box-shadow: 0 2px 3px -1px currentColor;
    }
    @supports (color: color-mix(in lab, red, red)) {
      .status {
        background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .status {
        color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
    @supports (color: color-mix(in lab, red, red)) {
      .status {
        box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
      }
    }
  }
  .join {
    display: inline-flex;
    align-items: stretch;
    --join-ss: 0;
    --join-se: 0;
    --join-es: 0;
    --join-ee: 0;
  }
  .join :where(.join-item) {
    border-start-start-radius: var(--join-ss, 0);
    border-start-end-radius: var(--join-se, 0);
    border-end-start-radius: var(--join-es, 0);
    border-end-end-radius: var(--join-ee, 0);
  }
  .join :where(.join-item) * {
    --join-ss: var(--radius-field);
    --join-se: var(--radius-field);
    --join-es: var(--radius-field);
    --join-ee: var(--radius-field);
  }
  .join > .join-item:where(:first-child) {
    --join-ss: var(--radius-field);
    --join-se: 0;
    --join-es: var(--radius-field);
    --join-ee: 0;
  }
  .join :first-child:not(:last-child) :where(.join-item) {
    --join-ss: var(--radius-field);
    --join-se: 0;
    --join-es: var(--radius-field);
    --join-ee: 0;
  }
  .join > .join-item:where(:last-child) {
    --join-ss: 0;
    --join-se: var(--radius-field);
    --join-es: 0;
    --join-ee: var(--radius-field);
  }
  .join :last-child:not(:first-child) :where(.join-item) {
    --join-ss: 0;
    --join-se: var(--radius-field);
    --join-es: 0;
    --join-ee: var(--radius-field);
  }
  .join > .join-item:where(:only-child) {
    --join-ss: var(--radius-field);
    --join-se: var(--radius-field);
    --join-es: var(--radius-field);
    --join-ee: var(--radius-field);
  }
  .join :only-child :where(.join-item) {
    --join-ss: var(--radius-field);
    --join-se: var(--radius-field);
    --join-es: var(--radius-field);
    --join-ee: var(--radius-field);
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .table {
    display: table;
  }
  .table-cell {
    display: table-cell;
  }
  @layer daisyui.modifier {
    .btn-circle {
      border-radius: calc(infinity * 1px);
      padding-inline: calc(0.25rem * 0);
      width: var(--size);
      height: var(--size);
    }
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  @layer daisyui.modifier {
    .loading-xl {
      width: calc(var(--size-selector, 0.25rem) * 8);
    }
  }
  .w-0 {
    width: calc(var(--spacing) * 0);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-1\/6 {
    width: calc(1/6 * 100%);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-full {
    width: 100%;
  }
  .w-max {
    width: max-content;
  }
  .flex-1 {
    flex: 1;
  }
  .table-fixed {
    table-layout: fixed;
  }
  @layer daisyui.component {
    .link {
      cursor: pointer;
      text-decoration-line: underline;
    }
    .link:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
    @media (forced-colors: active) {
      .link:focus {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    .link:focus-visible {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  @layer daisyui.modifier {
    .loading-spinner {
      -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
      mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
    }
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .break-all {
    word-break: break-all;
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .underline {
    text-decoration-line: underline;
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  @layer daisyui.modifier {
    .btn-outline:not(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
    @media (hover: none) {
      .btn-outline:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-fg: var(--btn-color);
        --btn-border: var(--btn-color);
        --btn-noise: none;
      }
    }
  }
  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }
  @media (width >= 40rem) {
    .sm\:table-cell {
      display: table-cell;
    }
  }
  @media (width >= 40rem) {
    .sm\:w-1\/2 {
      width: calc(1/2 * 100%);
    }
  }
  @media (width >= 40rem) {
    .sm\:w-1\/4 {
      width: calc(1/4 * 100%);
    }
  }
  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
}
@font-face {
  font-family: "Material Icons";
  src: url(/MaterialIcons-Regular.ttf) format("truetype");
}
.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
}
h1,
.h1 {
  margin-inline: calc(var(--spacing) * 2);
  padding-top: calc(var(--spacing) * 4);
  padding-bottom: calc(var(--spacing) * 2);
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
  --tw-font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-normal);
  letter-spacing: -1.5px;
}
h2,
.h2 {
  margin-inline: calc(var(--spacing) * 2);
  padding-top: calc(var(--spacing) * 4);
  padding-bottom: calc(var(--spacing) * 2);
  font-size: var(--text-2xl);
  line-height: var(--tw-leading, var(--text-2xl--line-height));
  --tw-font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-normal);
  letter-spacing: -0.5px;
}
h3,
.h3 {
  margin-inline: calc(var(--spacing) * 2);
  padding-top: calc(var(--spacing) * 4);
  padding-bottom: calc(var(--spacing) * 2);
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
  --tw-font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0px;
}
h4,
.h4 {
  margin-inline: calc(var(--spacing) * 2);
  padding-top: calc(var(--spacing) * 2);
  padding-bottom: calc(var(--spacing) * 1);
  font-size: var(--text-lg);
  line-height: var(--tw-leading, var(--text-lg--line-height));
  --tw-font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.25px;
}
h5,
.h5 {
  margin-inline: calc(var(--spacing) * 2);
  padding-top: calc(var(--spacing) * 2);
  padding-bottom: calc(var(--spacing) * 1);
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.15px;
}
h6,
.h6 {
  margin-inline: calc(var(--spacing) * 2);
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0px;
}
hr {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
  --tw-border-style: solid;
  border-style: solid;
  border-color: var(--color-gray-400);
}
a {
  color: var(--color-blue-600);
  text-decoration-line: underline;
}
@layer base {
  :where(:root),
  :root:has(input.theme-controller[value=light]:checked),
  [data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
      color-scheme: dark;
      --color-base-100: oklch(25.33% 0.016 252.42);
      --color-base-200: oklch(23.26% 0.014 253.1);
      --color-base-300: oklch(21.15% 0.012 254.09);
      --color-base-content: oklch(97.807% 0.029 256.847);
      --color-primary: oklch(58% 0.233 277.117);
      --color-primary-content: oklch(96% 0.018 272.314);
      --color-secondary: oklch(65% 0.241 354.308);
      --color-secondary-content: oklch(94% 0.028 342.258);
      --color-accent: oklch(77% 0.152 181.912);
      --color-accent-content: oklch(38% 0.063 188.416);
      --color-neutral: oklch(14% 0.005 285.823);
      --color-neutral-content: oklch(92% 0.004 286.32);
      --color-info: oklch(74% 0.16 232.661);
      --color-info-content: oklch(29% 0.066 243.157);
      --color-success: oklch(76% 0.177 163.223);
      --color-success-content: oklch(37% 0.077 168.94);
      --color-warning: oklch(82% 0.189 84.429);
      --color-warning-content: oklch(41% 0.112 45.904);
      --color-error: oklch(71% 0.194 13.428);
      --color-error-content: oklch(27% 0.105 12.094);
      --radius-selector: 0.5rem;
      --radius-field: 0.25rem;
      --radius-box: 0.5rem;
      --size-selector: 0.25rem;
      --size-field: 0.25rem;
      --border: 1px;
      --depth: 1;
      --noise: 0;
    }
  }
}
@layer base {
  :root:has(input.theme-controller[value=light]:checked),
  [data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),
  [data-theme=dark] {
    color-scheme: dark;
    --color-base-100: oklch(25.33% 0.016 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='200' height='200' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }
}
@layer base {
  :root {
    scrollbar-color: currentColor #0000;
  }
  @supports (color: color-mix(in lab, red, red)) {
    :root {
      scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
    }
  }
}
@layer base {
  @property --radialprogress { syntax: "<percentage>"; inherits: true; initial-value: 0%; }
}
@layer base {
  :root:has(.modal-open, .modal[open], .modal:target, .modal-toggle:checked) {
    overflow: hidden;
    --page-has-backdrop: 1;
  }
  :root:has(.drawer:not([class*=drawer-open]) > .drawer-toggle:checked) {
    overflow: hidden;
    --page-has-backdrop: 1;
  }
  @media (width < 40rem) {
    :root:has(.sm\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width < 40rem) {
    :root:has(.sm\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width < 48rem) {
    :root:has(.md\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width < 48rem) {
    :root:has(.md\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width < 64rem) {
    :root:has(.lg\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width < 64rem) {
    :root:has(.lg\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width < 80rem) {
    :root:has(.xl\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width < 80rem) {
    :root:has(.xl\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width < 96rem) {
    :root:has(.\32xl\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width < 96rem) {
    :root:has(.\32xl\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width >= 40rem) {
    :root:has(.max-sm\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width >= 40rem) {
    :root:has(.max-sm\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width >= 48rem) {
    :root:has(.max-md\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width >= 48rem) {
    :root:has(.max-md\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width >= 64rem) {
    :root:has(.max-lg\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width >= 64rem) {
    :root:has(.max-lg\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width >= 80rem) {
    :root:has(.max-xl\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width >= 80rem) {
    :root:has(.max-xl\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
  @media (width >= 96rem) {
    :root:has(.max-2xl\:drawer-open > .drawer-toggle:checked) {
      overflow: hidden;
    }
  }
  @media (width >= 96rem) {
    :root:has(.max-2xl\:drawer-open > .drawer-toggle:checked) {
      --page-has-backdrop: 1;
    }
  }
}
@layer base {
  :root:has(.modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked) {
    background-image: linear-gradient(var(--root-bg), var(--root-bg));
    background-color: var(--root-bg);
    animation: set-page-has-scroll forwards;
    animation-timeline: scroll();
    scrollbar-gutter: stable;
    scrollbar-gutter: if(style(--page-has-scroll: 1): stable; else: unset);
  }
  @supports (color: color-mix(in lab, red, red)) {
    :root:has(.modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked) {
      background-color: color-mix(in srgb, var(--root-bg), oklch(0% 0 0) calc(var(--page-has-backdrop, 0) * 40%));
    }
  }
  @keyframes set-page-has-scroll {
    0%, to {
      --page-has-scroll: 1;
    }
  }
}
@layer base {
  :root,
  [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }
  :where(:root, [data-theme]) {
    --root-bg: var(--color-base-100);
  }
}
@keyframes rating {
  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
@keyframes dropdown {
  0% {
    opacity: 0;
  }
}
@keyframes radio {
  0% {
    padding: 5px;
  }
  50% {
    padding: 3px;
  }
}
@keyframes toast {
  0% {
    scale: 0.9;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}
@keyframes skeleton {
  0% {
    background-position: 150%;
  }
  100% {
    background-position: -50%;
  }
}
@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}
@property --tw-space-y-reverse { syntax: "*"; inherits: false; initial-value: 0; }
@property --tw-border-style { syntax: "*"; inherits: false; initial-value: solid; }
@property --tw-blur { syntax: "*"; inherits: false; }
@property --tw-brightness { syntax: "*"; inherits: false; }
@property --tw-contrast { syntax: "*"; inherits: false; }
@property --tw-grayscale { syntax: "*"; inherits: false; }
@property --tw-hue-rotate { syntax: "*"; inherits: false; }
@property --tw-invert { syntax: "*"; inherits: false; }
@property --tw-opacity { syntax: "*"; inherits: false; }
@property --tw-saturate { syntax: "*"; inherits: false; }
@property --tw-sepia { syntax: "*"; inherits: false; }
@property --tw-drop-shadow { syntax: "*"; inherits: false; }
@property --tw-drop-shadow-color { syntax: "*"; inherits: false; }
@property --tw-drop-shadow-alpha { syntax: "<percentage>"; inherits: false; initial-value: 100%; }
@property --tw-drop-shadow-size { syntax: "*"; inherits: false; }
@property --tw-font-weight { syntax: "*"; inherits: false; }
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *,
    ::before,
    ::after,
    ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-font-weight: initial;
    }
  }
}

/* angular:styles/global:styles */
