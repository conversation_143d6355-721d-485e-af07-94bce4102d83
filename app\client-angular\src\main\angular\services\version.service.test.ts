import { describe, it, expect, vi, beforeEach } from "vitest";
import { TestBed } from "@angular/core/testing";
import { HttpClient } from "@angular/common/http";
import { of, throwError } from "rxjs";
import { VersionService } from "./version.service";
import { Version } from "../types/version.type";

vi.mock("../app.routes", () => ({
  backendUrl: () => "http://localhost:8080",
}));

vi.mock("../utils/log", () => ({
  tapLog: () => (source: any) => source,
}));

describe("VersionService", () => {
  let service: VersionService;
  let httpClientMock: any;

  const mockVersion: Version = {
    version: "1.2.0",
  };

  beforeEach(() => {
    // Create a mock HttpClient
    httpClientMock = {
      get: vi.fn(),
    };

    // Configure TestBed
    TestBed.configureTestingModule({
      providers: [
        VersionService,
        { provide: HttpClient, useValue: httpClientMock },
      ],
    });

    service = TestBed.inject(VersionService);
  });

  it("should be created", () => {
    expect(service).toBeTruthy();
    expect(service["httpClient"]).toBeDefined();
  });

  describe("loadVersion", () => {
    it("should load version successfully", (done) => {
      httpClientMock.get.mockReturnValue(of(mockVersion));
      service.loadVersion().subscribe({
        next: (body) => {
          // Assert
          expect(httpClientMock.get).toHaveBeenCalledWith(
            "http://localhost:8080/version"
          );
          expect(body).toEqual(mockVersion);
        },
      });
    });

    it("should handle errors gracefully", (done) => {
      const notFoundError = {
        status: 404,
        message: "Not Found",
      };
      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));
      service.loadVersion().subscribe({
        error: (err) => {
          expect(httpClientMock.get).toHaveBeenCalledWith(
            "http://localhost:8080/version"
          );
          expect(err).toBe(notFoundError);
        },
      });
    });
  });
});
