# https://docs.github.com/en/actions/reference/workflows-and-actions/workflow-syntax#name
name: build

# https://docs.github.com/en/actions/reference/workflows-and-actions/workflow-syntax#on
on: 
  # run on push to the default branch and tags
  push:
    branches:
      - 'master'
      - 'main'
    tags:
      - '[0-9]+.[0-9]+'
  # run on pull requests
  pull_request:
    branches:
      - 'master'
      - 'main'
  # run manually
  workflow_dispatch:

# https://docs.github.com/en/actions/reference/workflows-and-actions/workflow-syntax#jobs
jobs:
  build:
    runs-on: ubuntu-latest
    steps:

      # https://github.com/actions/checkout
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0

      # https://github.com/actions/setup-node
      - uses: actions/setup-node@v5
        with:
          node-version: "22"

      # https://github.com/actions/setup-java
      - uses: actions/setup-java@v5
        with:
          distribution: "temurin"
          java-version: "21"
          cache: "gradle"

      # https://github.com/gradle/actions
      - uses: gradle/actions/setup-gradle@v4
        with:
          gradle-version: "8.14"

      - name: Check Version
        run: gradle versionTag versionCheck

      - name: Build Node
        run: |
          sh -xv app/client-angular/build.npm.sh ci
          sh -xv app/client-svelte/build.npm.sh ci

      - name: Build Java
        run: |
          gradle spotlessCheck
          gradle build --parallel
          gradle jacocoTestReport

      - name: Build Images
        run: gradle buildImage

      - name: Fetch playwright version
        id: playwright
        run: sh -xv playwright.fetch.sh >> "$GITHUB_OUTPUT"

      - name: Cache playwright browsers
        id: playwright-cache
        uses: actions/cache@v4
        with:
          key: playwright-${{ steps.playwright.outputs.version }}
          path: |
            ~/.cache/ms-playwright

      - name: Install playwright browsers
        if: steps.playwright-cache.outputs.cache-hit != 'true'
        run: sh -xv playwright.setup.sh

      - name: Run playwright tests
        run: sh -xv playwright.test.sh

      # https://github.com/actions/upload-artifact
      - uses: actions/upload-artifact@v4
        if: ${{ failure() }}
        with:
          name: pages
          path: pages/
          retention-days: 7

        # https://github.com/features/packages
      - if: startsWith(github.ref, 'refs/tags')
        name: Update Packages (tag)
        run: |
          echo "${{ github.token }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
          IMAGE_REPOSITORY_FROM=${{ github.event.repository.name }}
          IMAGE_REPOSITORY_TO=`echo "ghcr.io/${{ github.repository }}" | tr '[:upper:]' '[:lower:]'`
          IMAGE_TAG=${GITHUB_REF#refs/tags/}
          for IMAGE_NAME in client server
          do
            docker tag ${IMAGE_REPOSITORY_FROM}/${IMAGE_NAME}:${IMAGE_TAG} ${IMAGE_REPOSITORY_TO}/${IMAGE_NAME}:${IMAGE_TAG}
            docker push ${IMAGE_REPOSITORY_TO}/${IMAGE_NAME}:${IMAGE_TAG}
          done

      # https://github.com/peaceiris/actions-gh-pages
      - if: startsWith(github.ref, 'refs/tags')
        name: Update Pages (tag)
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: pages

      # https://cli.github.com/manual/gh_release
      - if: startsWith(github.ref, 'refs/tags')
        name: Create Release (tag)
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gradle buildChangelog
          gh release create "${GITHUB_REF#refs/tags/}" \
            --title "Version ${GITHUB_REF#refs/tags/}" \
            --verify-tag \
            --fail-on-no-commits \
            --notes-file build/reports/CHANGELOG.md

