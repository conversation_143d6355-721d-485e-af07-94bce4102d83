import {
  TestBed
} from "./chunk-JBEFZW42.js";
import {
  HttpClient,
  VersionService
} from "./chunk-GVSGB5JR.js";
import "./chunk-PRSR237C.js";
import {
  of,
  provideZonelessChangeDetection,
  throwError
} from "./chunk-SG4F3HSD.js";

// src/main/angular/services/version.service.spec.ts
import { describe, it, expect, vi, beforeEach } from "vitest";
var VERSION = {
  version: "1.2.0"
};
describe("VersionService", () => {
  let versionService;
  let httpClientMock;
  beforeEach(() => {
    httpClientMock = {
      get: vi.fn()
    };
    TestBed.configureTestingModule({
      providers: [
        VersionService,
        { provide: HttpClient, useValue: httpClientMock },
        provideZonelessChangeDetection()
      ]
    });
    versionService = TestBed.inject(VersionService);
  });
  it("should be created", () => {
    expect(versionService).toBeTruthy();
    expect(versionService["httpClient"]).toBeDefined();
  });
  describe("loadVersion", () => {
    it("should load version successfully", (done) => {
      httpClientMock.get.mockReturnValue(of(VERSION));
      versionService.loadVersion().subscribe({
        next: (body) => {
          expect(httpClientMock.get).toHaveBeenCalledWith("http://localhost:8080/version");
          expect(body).toEqual(VERSION);
        }
      });
    });
    it("should handle errors gracefully", (done) => {
      const notFoundError = {
        status: 404,
        message: "Not Found"
      };
      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));
      versionService.loadVersion().subscribe({
        error: (err) => {
          expect(httpClientMock.get).toHaveBeenCalledWith("http://localhost:8080/version");
          expect(err).toBe(notFoundError);
        }
      });
    });
  });
});
//# sourceMappingURL=spec-version.service.spec.js.map
