import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TestBed } from '@angular/core/testing';
import { HttpClient } from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { EnumService, filterByCriteria } from './enum.service';
import { EnumItem } from '../types/enum.type';

// Mock the backendUrl function
vi.mock('../app.routes', () => ({
  backendUrl: () => 'http://localhost:8080'
}));

// Mock the tapLog utility
vi.mock('../utils/log', () => ({
  tapLog: () => (source: any) => source
}));

describe('EnumService', () => {
  let service: EnumService;
  let httpClientMock: any;

  const mockEnumItems: EnumItem[] = [
    { code: 1, name: 'cat', text: 'Cat' },
    { code: 2, name: 'dog', text: '<PERSON>' },
    { code: 3, name: 'bird', text: '<PERSON>' }
  ];

  const mockApiResponse = { content: mockEnumItems };

  beforeEach(() => {
    // Create a mock HttpClient
    httpClientMock = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn()
    };

    // Configure TestBed
    TestBed.configureTestingModule({
      providers: [
        EnumService,
        { provide: HttpClient, useValue: httpClientMock }
      ]
    });

    service = TestBed.inject(EnumService);
  });

  describe('loadAllEnum', () => {
    it('should load all enum items for a given art', (done) => {
      // Arrange
      const art = 'species';
      httpClientMock.get.mockReturnValue(of(mockApiResponse));

      // Act
      service.loadAllEnum(art).subscribe({
        next: (result) => {
          // Assert
          expect(httpClientMock.get).toHaveBeenCalledWith('http://localhost:8080/api/enum/species');
          expect(result).toEqual(mockEnumItems);
          done();
        }
      });
    });

    it('should handle API errors gracefully', (done) => {
      // Arrange
      const art = 'species';
      const error = new Error('API Error');
      httpClientMock.get.mockReturnValue(throwError(() => error));

      // Act
      service.loadAllEnum(art).subscribe({
        error: (err) => {
          // Assert
          expect(httpClientMock.get).toHaveBeenCalledWith('http://localhost:8080/api/enum/species');
          expect(err).toBe(error);
          done();
        }
      });
    });
  });

  describe('createEnum', () => {
    it('should create a new enum item', (done) => {
      // Arrange
      const art = 'species';
      const newItem: EnumItem = { code: 4, name: 'fish', text: 'Fish' };
      httpClientMock.post.mockReturnValue(of(newItem));

      // Act
      service.createEnum(art, newItem).subscribe({
        next: (result) => {
          // Assert
          expect(httpClientMock.post).toHaveBeenCalledWith('http://localhost:8080/api/enum/species', newItem);
          expect(result).toEqual(newItem);
          done();
        }
      });
    });

    it('should handle creation errors', (done) => {
      // Arrange
      const art = 'species';
      const newItem: EnumItem = { code: 4, name: 'fish', text: 'Fish' };
      const error = new Error('Creation failed');
      httpClientMock.post.mockReturnValue(throwError(() => error));

      // Act
      service.createEnum(art, newItem).subscribe({
        error: (err) => {
          // Assert
          expect(httpClientMock.post).toHaveBeenCalledWith('http://localhost:8080/api/enum/species', newItem);
          expect(err).toBe(error);
          done();
        }
      });
    });
  });

  describe('updateEnum', () => {
    it('should update an existing enum item', (done) => {
      // Arrange
      const art = 'species';
      const updatedItem: EnumItem = { code: 1, name: 'cat', text: 'Updated Cat' };
      httpClientMock.put.mockReturnValue(of(updatedItem));

      // Act
      service.updateEnum(art, updatedItem).subscribe({
        next: (result) => {
          // Assert
          expect(httpClientMock.put).toHaveBeenCalledWith('http://localhost:8080/api/enum/species/1', updatedItem);
          expect(result).toEqual(updatedItem);
          done();
        }
      });
    });

    it('should handle update errors', (done) => {
      // Arrange
      const art = 'species';
      const updatedItem: EnumItem = { code: 1, name: 'cat', text: 'Updated Cat' };
      const error = new Error('Update failed');
      httpClientMock.put.mockReturnValue(throwError(() => error));

      // Act
      service.updateEnum(art, updatedItem).subscribe({
        error: (err) => {
          // Assert
          expect(httpClientMock.put).toHaveBeenCalledWith('http://localhost:8080/api/enum/species/1', updatedItem);
          expect(err).toBe(error);
          done();
        }
      });
    });
  });

  describe('removeEnum', () => {
    it('should remove an enum item by code', (done) => {
      // Arrange
      const art = 'species';
      const code = 1;
      const deletedItem: EnumItem = { code: 1, name: 'cat', text: 'Cat' };
      httpClientMock.delete.mockReturnValue(of(deletedItem));

      // Act
      service.removeEnum(art, code).subscribe({
        next: (result) => {
          // Assert
          expect(httpClientMock.delete).toHaveBeenCalledWith('http://localhost:8080/api/enum/species/1');
          expect(result).toEqual(deletedItem);
          done();
        }
      });
    });

    it('should handle deletion errors', (done) => {
      // Arrange
      const art = 'species';
      const code = 1;
      const error = new Error('Deletion failed');
      httpClientMock.delete.mockReturnValue(throwError(() => error));

      // Act
      service.removeEnum(art, code).subscribe({
        error: (err) => {
          // Assert
          expect(httpClientMock.delete).toHaveBeenCalledWith('http://localhost:8080/api/enum/species/1');
          expect(err).toBe(error);
          done();
        }
      });
    });
  });
});

describe('filterByCriteria', () => {
  const testItems: EnumItem[] = [
    { code: 1, name: 'cat', text: 'Domestic Cat' },
    { code: 2, name: 'dog', text: 'Domestic Dog' },
    { code: 3, name: 'bird', text: 'Wild Bird' }
  ];

  it('should return all items when criteria is null', () => {
    // Act
    const result = testItems.filter(filterByCriteria(null));

    // Assert
    expect(result).toEqual(testItems);
  });

  it('should return all items when criteria is undefined', () => {
    // Act
    const result = testItems.filter(filterByCriteria(undefined));

    // Assert
    expect(result).toEqual(testItems);
  });

  it('should return all items when criteria is empty string', () => {
    // Act
    const result = testItems.filter(filterByCriteria(''));

    // Assert
    expect(result).toEqual(testItems);
  });

  it('should filter by name (case insensitive)', () => {
    // Act
    const result = testItems.filter(filterByCriteria('CAT'));

    // Assert
    expect(result).toEqual([{ code: 1, name: 'cat', text: 'Domestic Cat' }]);
  });

  it('should filter by text (case insensitive)', () => {
    // Act
    const result = testItems.filter(filterByCriteria('domestic'));

    // Assert
    expect(result).toEqual([
      { code: 1, name: 'cat', text: 'Domestic Cat' },
      { code: 2, name: 'dog', text: 'Domestic Dog' }
    ]);
  });

  it('should return empty array when no matches found', () => {
    // Act
    const result = testItems.filter(filterByCriteria('elephant'));

    // Assert
    expect(result).toEqual([]);
  });

  it('should match partial strings at the beginning', () => {
    // Act
    const result = testItems.filter(filterByCriteria('bir'));

    // Assert
    expect(result).toEqual([{ code: 3, name: 'bird', text: 'Wild Bird' }]);
  });

  it('should not match partial strings in the middle', () => {
    // Act
    const result = testItems.filter(filterByCriteria('ird'));

    // Assert
    expect(result).toEqual([]);
  });
});
