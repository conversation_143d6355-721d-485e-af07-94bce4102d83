import {
  DefaultValueAccessor,
  FormControl,
  FormControlName,
  FormGroup,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NumberValueAccessor,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-K5TBLY2U.js";
import {
  HttpClient,
  backendUrl,
  tapLog
} from "./chunk-2BWCZPUI.js";
import {
  CommonModule,
  UpperCasePipe
} from "./chunk-ZMC4WIII.js";
import "./chunk-PRSR237C.js";
import {
  Component,
  DestroyRef,
  Injectable,
  Input,
  Output,
  __spreadProps,
  __spreadValues,
  computed,
  inject,
  input,
  map,
  model,
  output,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵconditionalCreate,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-SG4F3HSD.js";

// src/main/angular/services/enum.service.ts
var EnumService = class _EnumService {
  httpClient = inject(HttpClient);
  loadAllEnum(art) {
    const path = [backendUrl(), "api", "enum", art].join("/");
    return this.httpClient.get(path).pipe(tapLog("GET", path), map((body) => body.content));
  }
  createEnum(art, item) {
    const path = [backendUrl(), "api", "enum", art].join("/");
    return this.httpClient.post(path, item).pipe(tapLog("POST", path));
  }
  updateEnum(art, item) {
    const path = [backendUrl(), "api", "enum", art, item.code].join("/");
    return this.httpClient.put(path, item).pipe(tapLog("PUT", path));
  }
  removeEnum(art, code) {
    const path = [backendUrl(), "api", "enum", art, code].join("/");
    return this.httpClient.delete(path).pipe(tapLog("DELETE", path));
  }
  static \u0275fac = function EnumService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _EnumService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _EnumService, factory: _EnumService.\u0275fac });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EnumService, [{
    type: Injectable
  }], null, null);
})();
function filterByCriteria(criteria) {
  return (item) => {
    if (criteria) {
      if (item.name.toLowerCase().startsWith(criteria.toLowerCase()))
        return true;
      if (item.text.toLowerCase().startsWith(criteria.toLowerCase()))
        return true;
      return false;
    }
    return true;
  };
}

// src/main/angular/pages/basis/enum-editor/enum-editor.ts
var EnumEditorComponent = class _EnumEditorComponent {
  destroyRef = inject(DestroyRef);
  restApi = inject(EnumService);
  art = input.required(...ngDevMode ? [{ debugName: "art" }] : []);
  mode = input.required(...ngDevMode ? [{ debugName: "mode" }] : []);
  visible = model.required(...ngDevMode ? [{ debugName: "visible" }] : []);
  item = input.required(...ngDevMode ? [{ debugName: "item" }] : []);
  form = new FormGroup({
    code: new FormControl(0, Validators.required),
    name: new FormControl("", Validators.required),
    text: new FormControl("", Validators.required)
  });
  ngOnInit() {
    this.form.patchValue(this.item());
  }
  get isSubmittable() {
    return this.form.dirty && this.form.valid;
  }
  cancelEmitter = output({ alias: "cancel" });
  onCancelClicked() {
    this.cancelEmitter.emit(this.item());
    this.visible.set(false);
    this.form.reset();
  }
  createEmitter = output({ alias: "create" });
  updateEmitter = output({ alias: "update" });
  onSubmitClicked() {
    if (this.mode() === "create") {
      const subscription = this.restApi.createEnum(this.art(), __spreadProps(__spreadValues({}, this.item()), {
        name: this.form.value.name,
        text: this.form.value.text
      })).subscribe({
        next: (item) => {
          this.createEmitter.emit(item);
          this.visible.set(false);
          this.form.reset();
        }
      });
      this.destroyRef.onDestroy(() => {
        subscription.unsubscribe();
      });
    } else {
      const subscription = this.restApi.updateEnum(this.art(), __spreadProps(__spreadValues({}, this.item()), {
        name: this.form.value.name,
        text: this.form.value.text
      })).subscribe({
        next: (item) => {
          this.updateEmitter.emit(item);
          this.visible.set(false);
          this.form.reset();
        }
      });
      this.destroyRef.onDestroy(() => {
        subscription.unsubscribe();
      });
    }
  }
  static \u0275fac = function EnumEditorComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _EnumEditorComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _EnumEditorComponent, selectors: [["app-enum-editor"]], inputs: { art: [1, "art"], mode: [1, "mode"], visible: [1, "visible"], item: [1, "item"] }, outputs: { visible: "visibleChange", cancelEmitter: "cancel", createEmitter: "create", updateEmitter: "update" }, decls: 23, vars: 2, consts: [[3, "ngSubmit", "formGroup"], [1, "flex", "flex-col", "gap-2", "pt-4"], [1, "w-full", "flex", "flex-row", "gap-1", "items-baseline"], [1, "w-1/6"], [1, "floating-label"], [1, "label"], ["aria-label", "Code", "type", "number", "formControlName", "code", "readonly", "", 1, "input", "input-bordered", "w-full"], [1, "w-full"], ["aria-label", "Name", "type", "text", "placeholder", "Enter a name", "formControlName", "name", 1, "input", "input-bordered", "w-full"], ["aria-label", "Text", "placeholder", "Enter a text", "formControlName", "text", 1, "textarea", "w-full"], [1, "join", "py-4"], ["type", "submit", 1, "btn", "join-item", 3, "disabled"], ["type", "button", 1, "btn", "join-item", 3, "click"]], template: function EnumEditorComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "form", 0);
      \u0275\u0275listener("ngSubmit", function EnumEditorComponent_Template_form_ngSubmit_0_listener() {
        return ctx.onSubmitClicked();
      });
      \u0275\u0275elementStart(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "label", 4)(5, "span", 5);
      \u0275\u0275text(6, "Code");
      \u0275\u0275elementEnd();
      \u0275\u0275element(7, "input", 6);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 7)(9, "label", 4)(10, "span", 5);
      \u0275\u0275text(11, "Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(12, "input", 8);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(13, "div", 7)(14, "label", 4)(15, "span", 5);
      \u0275\u0275text(16, "Text");
      \u0275\u0275elementEnd();
      \u0275\u0275element(17, "textarea", 9);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(18, "div", 10)(19, "button", 11);
      \u0275\u0275text(20, " Ok ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "button", 12);
      \u0275\u0275listener("click", function EnumEditorComponent_Template_button_click_21_listener() {
        return ctx.onCancelClicked();
      });
      \u0275\u0275text(22, " Cancel ");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275property("formGroup", ctx.form);
      \u0275\u0275advance(19);
      \u0275\u0275property("disabled", !ctx.isSubmittable);
    }
  }, dependencies: [ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NumberValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EnumEditorComponent, [{
    type: Component,
    args: [{ selector: "app-enum-editor", imports: [ReactiveFormsModule], template: '<form [formGroup]="form" (ngSubmit)="onSubmitClicked()">\r\n  <div class="flex flex-col gap-2 pt-4">\r\n    <div class="w-full flex flex-row gap-1 items-baseline">\r\n      <div class="w-1/6">\r\n        <label class="floating-label">\r\n          <span class="label">Code</span>\r\n          <input\r\n            aria-label="Code"\r\n            type="number"\r\n            class="input input-bordered w-full"\r\n            formControlName="code"\r\n            readonly\r\n          />\r\n        </label>\r\n      </div>\r\n      <div class="w-full">\r\n        <label class="floating-label">\r\n          <span class="label">Name</span>\r\n          <input\r\n            aria-label="Name"\r\n            type="text"\r\n            class="input input-bordered w-full"\r\n            placeholder="Enter a name"\r\n            formControlName="name"\r\n          />\r\n        </label>\r\n      </div>\r\n    </div>\r\n    <div class="w-full">\r\n      <label class="floating-label">\r\n        <span class="label">Text</span>\r\n        <textarea\r\n          aria-label="Text"\r\n          class="textarea w-full"\r\n          placeholder="Enter a text"\r\n          formControlName="text"\r\n        ></textarea>\r\n      </label>\r\n    </div>\r\n  </div>\r\n  <div class="join py-4">\r\n    <button type="submit" class="btn join-item" [disabled]="!isSubmittable">\r\n      Ok\r\n    </button>\r\n    <button type="button" class="btn join-item" (click)="onCancelClicked()">\r\n      Cancel\r\n    </button>\r\n  </div>\r\n</form>\r\n' }]
  }], null, { art: [{ type: Input, args: [{ isSignal: true, alias: "art", required: true }] }], mode: [{ type: Input, args: [{ isSignal: true, alias: "mode", required: true }] }], visible: [{ type: Input, args: [{ isSignal: true, alias: "visible", required: true }] }, { type: Output, args: ["visibleChange"] }], item: [{ type: Input, args: [{ isSignal: true, alias: "item", required: true }] }], cancelEmitter: [{ type: Output, args: ["cancel"] }], createEmitter: [{ type: Output, args: ["create"] }], updateEmitter: [{ type: Output, args: ["update"] }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(EnumEditorComponent, { className: "EnumEditorComponent", filePath: "src/main/angular/pages/basis/enum-editor/enum-editor.ts", lineNumber: 25 });
})();

// src/main/angular/pages/basis/enum-lister/enum-lister.ts
var _forTrack0 = ($index, $item) => $item.code;
function EnumListerComponent_Conditional_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275element(1, "span", 10);
    \u0275\u0275elementEnd();
  }
}
function EnumListerComponent_Conditional_14_Conditional_17_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr")(1, "td", 19)(2, "app-enum-editor", 20);
    \u0275\u0275listener("create", function EnumListerComponent_Conditional_14_Conditional_17_Template_app_enum_editor_create_2_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.afterCreateItem($event));
    });
    \u0275\u0275twoWayListener("visibleChange", function EnumListerComponent_Conditional_14_Conditional_17_Template_app_enum_editor_visibleChange_2_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      \u0275\u0275twoWayBindingSet(ctx_r1.itemEditorCreate, $event) || (ctx_r1.itemEditorCreate = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("art", ctx_r1.art());
    \u0275\u0275twoWayProperty("visible", ctx_r1.itemEditorCreate);
    \u0275\u0275property("item", ctx_r1.newItem());
  }
}
function EnumListerComponent_Conditional_14_For_19_Conditional_18_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr")(1, "td", 19)(2, "app-enum-editor", 28);
    \u0275\u0275listener("update", function EnumListerComponent_Conditional_14_For_19_Conditional_18_Template_app_enum_editor_update_2_listener($event) {
      \u0275\u0275restoreView(_r6);
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.afterUpdateItem($event));
    });
    \u0275\u0275twoWayListener("visibleChange", function EnumListerComponent_Conditional_14_For_19_Conditional_18_Template_app_enum_editor_visibleChange_2_listener($event) {
      \u0275\u0275restoreView(_r6);
      const ctx_r1 = \u0275\u0275nextContext(3);
      \u0275\u0275twoWayBindingSet(ctx_r1.itemEditorUpdate, $event) || (ctx_r1.itemEditorUpdate = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const item_r5 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("art", ctx_r1.art());
    \u0275\u0275twoWayProperty("visible", ctx_r1.itemEditorUpdate);
    \u0275\u0275property("item", item_r5);
  }
}
function EnumListerComponent_Conditional_14_For_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 21);
    \u0275\u0275listener("click", function EnumListerComponent_Conditional_14_For_19_Template_tr_click_0_listener() {
      const item_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onItemClicked(item_r5));
    });
    \u0275\u0275elementStart(1, "td", 22)(2, "span");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "td", 22)(5, "span");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "td", 23)(8, "span");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(10, "td", 24)(11, "div", 25)(12, "button", 26);
    \u0275\u0275listener("click", function EnumListerComponent_Conditional_14_For_19_Template_button_click_12_listener() {
      const item_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onItemRemoveClicked(item_r5));
    });
    \u0275\u0275elementStart(13, "span", 7);
    \u0275\u0275text(14, "delete");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "button", 27);
    \u0275\u0275listener("click", function EnumListerComponent_Conditional_14_For_19_Template_button_click_15_listener() {
      const item_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onItemEditorUpdateClicked(item_r5));
    });
    \u0275\u0275elementStart(16, "span", 7);
    \u0275\u0275text(17, "edit");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275conditionalCreate(18, EnumListerComponent_Conditional_14_For_19_Conditional_18_Template, 3, 3, "tr");
  }
  if (rf & 2) {
    const item_r5 = ctx.$implicit;
    const $index_r7 = ctx.$index;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("border-l-2", ctx_r1.itemCode() === item_r5.code)("bg-gray-100", $index_r7 % 2 === 1);
    \u0275\u0275property("title", item_r5.text);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(item_r5.code);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(item_r5.name);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(item_r5.text);
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", ctx_r1.itemEditorDisabled());
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", ctx_r1.itemEditorDisabled());
    \u0275\u0275advance(3);
    \u0275\u0275conditional(ctx_r1.itemEditorUpdate() && ctx_r1.itemCode() === item_r5.code ? 18 : -1);
  }
}
function EnumListerComponent_Conditional_14_ForEmpty_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "tr")(1, "td", 29);
    \u0275\u0275text(2, "No items");
    \u0275\u0275elementEnd()();
  }
}
function EnumListerComponent_Conditional_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "table", 9)(1, "thead", 11)(2, "tr", 12)(3, "th", 13)(4, "span", 14);
    \u0275\u0275text(5, "Code");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "th", 15)(7, "span", 14);
    \u0275\u0275text(8, "Name");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "th", 16)(10, "span", 14);
    \u0275\u0275text(11, "Text");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "th", 17)(13, "button", 18);
    \u0275\u0275listener("click", function EnumListerComponent_Conditional_14_Template_button_click_13_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onItemEditorCreateClicked());
    });
    \u0275\u0275elementStart(14, "span", 7);
    \u0275\u0275text(15, "add");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275elementStart(16, "tbody");
    \u0275\u0275conditionalCreate(17, EnumListerComponent_Conditional_14_Conditional_17_Template, 3, 3, "tr");
    \u0275\u0275repeaterCreate(18, EnumListerComponent_Conditional_14_For_19_Template, 19, 11, null, null, _forTrack0, false, EnumListerComponent_Conditional_14_ForEmpty_20_Template, 3, 0, "tr");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(13);
    \u0275\u0275property("disabled", ctx_r1.itemEditorDisabled());
    \u0275\u0275advance(4);
    \u0275\u0275conditional(ctx_r1.itemEditorCreate() ? 17 : -1);
    \u0275\u0275advance();
    \u0275\u0275repeater(ctx_r1.allFilteredItem());
  }
}
var EnumListerComponent = class _EnumListerComponent {
  destroyRef = inject(DestroyRef);
  restApi = inject(EnumService);
  art = input.required(...ngDevMode ? [{ debugName: "art" }] : []);
  loading = signal(false, ...ngDevMode ? [{ debugName: "loading" }] : []);
  allItem = signal([], ...ngDevMode ? [{ debugName: "allItem" }] : []);
  afterCreateItem(newItem) {
    this.allItem.update((allItem) => {
      return [newItem, ...allItem];
    });
  }
  afterUpdateItem(newItem) {
    this.allItem.update((allItem) => {
      return allItem.map((item) => item.code === newItem.code ? newItem : item);
    });
  }
  afterRemoveItem(newItem) {
    this.allItem.update((allItem) => {
      return allItem.filter((item) => item.code !== newItem.code);
    });
  }
  filterForm = new FormGroup({
    criteria: new FormControl("", Validators.required)
  });
  allFilteredItem = computed(() => {
    return this.allItem().filter(filterByCriteria(this.filterForm.value.criteria));
  }, ...ngDevMode ? [{ debugName: "allFilteredItem" }] : []);
  newItem = computed(() => {
    return {
      code: Math.max(...this.allItem().map((item) => item.code)) + 1,
      name: "",
      text: ""
    };
  }, ...ngDevMode ? [{ debugName: "newItem" }] : []);
  ngOnInit() {
    this.onFilterClicked();
  }
  onFilterClicked() {
    this.loading.set(true);
    const subscription = this.restApi.loadAllEnum(this.art()).subscribe({
      next: (allItem) => {
        this.allItem.set(allItem);
      },
      complete: () => {
        this.loading.set(false);
      }
    });
    this.destroyRef.onDestroy(() => {
      subscription.unsubscribe();
    });
  }
  itemCode = signal(-1, ...ngDevMode ? [{ debugName: "itemCode" }] : []);
  // no item selected
  onItemClicked(item) {
    this.itemCode.set(item.code);
  }
  itemEditorCreate = signal(false, ...ngDevMode ? [{ debugName: "itemEditorCreate" }] : []);
  onItemEditorCreateClicked() {
    this.itemCode.set(-1);
    this.itemEditorCreate.set(true);
    this.itemEditorUpdate.set(false);
  }
  itemEditorUpdate = signal(false, ...ngDevMode ? [{ debugName: "itemEditorUpdate" }] : []);
  onItemEditorUpdateClicked(item) {
    this.itemCode.set(item.code);
    this.itemEditorCreate.set(false);
    this.itemEditorUpdate.set(true);
  }
  itemEditorDisabled = computed(() => this.itemEditorCreate() || this.itemEditorUpdate(), ...ngDevMode ? [{ debugName: "itemEditorDisabled" }] : []);
  onItemRemoveClicked(item) {
    this.itemCode.set(-1);
    const text = item.name;
    const hint = text.length > 20 ? text.substring(0, 20) + "..." : text;
    if (!confirm("Delete enum '" + hint + "' permanently?"))
      return;
    this.loading.set(true);
    const subscription = this.restApi.removeEnum(this.art(), item.code).subscribe({
      next: (item2) => {
        this.afterRemoveItem(item2);
      },
      complete: () => {
        this.loading.set(false);
      }
    });
    this.destroyRef.onDestroy(() => {
      subscription.unsubscribe();
    });
  }
  static \u0275fac = function EnumListerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _EnumListerComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _EnumListerComponent, selectors: [["app-enum-lister"]], inputs: { art: [1, "art"] }, decls: 15, vars: 6, consts: [[1, "flex", "flex-col", "gap-1", "ml-2", "mr-2"], [3, "ngSubmit", "formGroup"], [1, "flex", "flex-row", "gap-2", "items-center", "pb-2", "pr-2"], [1, "floating-label", "w-full"], [1, "label"], ["aria-label", "Filter", "type", "text", "placeholder", "Enter filter critria", "formControlName", "criteria", 1, "input", "input-bordered", "w-full"], ["type", "submit", "title", "Filter items", 1, "btn", "btn-circle", "btn-outline", 3, "disabled"], [1, "material-icons"], [1, "h-screen", "flex", "justify-center", "items-start"], [1, "table-fixed"], [1, "loading", "loading-spinner", "loading-xl"], [1, "justify-between"], [1, "bg-gray-200"], [1, "px-2", "py-3", "text-left", "w-48", "table-cell"], [1, "text-gray-600"], [1, "px-2", "py-3", "text-left", "w-full", "sm:w-1/4", "table-cell"], [1, "px-2", "py-3", "text-left", "w-0", "sm:w-1/2", "hidden", "sm:table-cell"], [1, "px-2", "py-3", "text-right", "w-0", "table-cell"], ["title", "Add a new item", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], ["colspan", "4", 1, "border-l-4", "px-2"], ["mode", "create", 3, "create", "visibleChange", "art", "visible", "item"], [3, "click", "title"], [1, "px-2", "py-3", "text-left", "table-cell"], [1, "px-2", "py-3", "text-left", "hidden", "sm:table-cell"], [1, "px-2", "py-3", "table-cell"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "items-center", "gap-1", "w-max"], ["title", "Delete an item", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], ["title", "Edit an item", 1, "btn", "btn-circle", "btn-outline", 3, "click", "disabled"], ["mode", "update", 3, "update", "visibleChange", "art", "visible", "item"], ["colspan", "4", 1, "px-2"]], template: function EnumListerComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "h1");
      \u0275\u0275text(1);
      \u0275\u0275pipe(2, "uppercase");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "div", 0)(4, "form", 1);
      \u0275\u0275listener("ngSubmit", function EnumListerComponent_Template_form_ngSubmit_4_listener() {
        return ctx.onFilterClicked();
      });
      \u0275\u0275elementStart(5, "div", 2)(6, "label", 3)(7, "span", 4);
      \u0275\u0275text(8, "Filter");
      \u0275\u0275elementEnd();
      \u0275\u0275element(9, "input", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "button", 6)(11, "span", 7);
      \u0275\u0275text(12, "search");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275conditionalCreate(13, EnumListerComponent_Conditional_13_Template, 2, 0, "div", 8)(14, EnumListerComponent_Conditional_14_Template, 21, 3, "table", 9);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275textInterpolate(\u0275\u0275pipeBind1(2, 4, ctx.art()));
      \u0275\u0275advance(3);
      \u0275\u0275property("formGroup", ctx.filterForm);
      \u0275\u0275advance(6);
      \u0275\u0275property("disabled", ctx.itemEditorDisabled());
      \u0275\u0275advance(3);
      \u0275\u0275conditional(ctx.loading() ? 13 : 14);
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, EnumEditorComponent, UpperCasePipe], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EnumListerComponent, [{
    type: Component,
    args: [{ selector: "app-enum-lister", imports: [CommonModule, ReactiveFormsModule, EnumEditorComponent], template: '<h1>{{ art() | uppercase }}</h1>\r\n\r\n<div class="flex flex-col gap-1 ml-2 mr-2">\r\n  <form [formGroup]="filterForm" (ngSubmit)="onFilterClicked()">\r\n    <div class="flex flex-row gap-2 items-center pb-2 pr-2">\r\n      <label class="floating-label w-full">\r\n        <span class="label">Filter</span>\r\n        <input\r\n          aria-label="Filter"\r\n          type="text"\r\n          class="input input-bordered w-full"\r\n          placeholder="Enter filter critria"\r\n          formControlName="criteria"\r\n        />\r\n      </label>\r\n      <button\r\n        type="submit"\r\n        title="Filter items"\r\n        class="btn btn-circle btn-outline"\r\n        [disabled]="itemEditorDisabled()"\r\n      >\r\n        <span class="material-icons">search</span>\r\n      </button>\r\n    </div>\r\n  </form>\r\n  @if (loading()) {\r\n    <div class="h-screen flex justify-center items-start">\r\n      <span class="loading loading-spinner loading-xl"></span>\r\n    </div>\r\n  } @else {\r\n    <table class="table-fixed">\r\n      <thead class="justify-between">\r\n        <tr class="bg-gray-200">\r\n          <th class="px-2 py-3 text-left w-48 table-cell">\r\n            <span class="text-gray-600">Code</span>\r\n          </th>\r\n          <th class="px-2 py-3 text-left w-full sm:w-1/4 table-cell">\r\n            <span class="text-gray-600">Name</span>\r\n          </th>\r\n          <th class="px-2 py-3 text-left w-0 sm:w-1/2 hidden sm:table-cell">\r\n            <span class="text-gray-600">Text</span>\r\n          </th>\r\n          <th class="px-2 py-3 text-right w-0 table-cell">\r\n            <button\r\n              title="Add a new item"\r\n              class="btn btn-circle btn-outline"\r\n              (click)="onItemEditorCreateClicked()"\r\n              [disabled]="itemEditorDisabled()"\r\n            >\r\n              <span class="material-icons">add</span>\r\n            </button>\r\n          </th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        @if (itemEditorCreate()) {\r\n          <tr>\r\n            <td class="border-l-4 px-2" colspan="4">\r\n              <app-enum-editor\r\n                mode="create"\r\n                (create)="afterCreateItem($event)"\r\n                [art]="art()"\r\n                [(visible)]="itemEditorCreate"\r\n                [item]="newItem()"\r\n              />\r\n            </td>\r\n          </tr>\r\n        }\r\n        @for (item of allFilteredItem(); track item.code) {\r\n          <tr\r\n            [title]="item.text"\r\n            [class.border-l-2]="itemCode() === item.code"\r\n            [class.bg-gray-100]="$index % 2 === 1"\r\n            (click)="onItemClicked(item)"\r\n          >\r\n            <td class="px-2 py-3 text-left table-cell">\r\n              <span>{{ item.code }}</span>\r\n            </td>\r\n            <td class="px-2 py-3 text-left table-cell">\r\n              <span>{{ item.name }}</span>\r\n            </td>\r\n            <td class="px-2 py-3 text-left hidden sm:table-cell">\r\n              <span>{{ item.text }}</span>\r\n            </td>\r\n            <td class="px-2 py-3 table-cell">\r\n              <div\r\n                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"\r\n              >\r\n                <button\r\n                  title="Delete an item"\r\n                  class="btn btn-circle btn-outline"\r\n                  (click)="onItemRemoveClicked(item)"\r\n                  [disabled]="itemEditorDisabled()"\r\n                >\r\n                  <span class="material-icons">delete</span>\r\n                </button>\r\n                <button\r\n                  title="Edit an item"\r\n                  class="btn btn-circle btn-outline"\r\n                  (click)="onItemEditorUpdateClicked(item)"\r\n                  [disabled]="itemEditorDisabled()"\r\n                >\r\n                  <span class="material-icons">edit</span>\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          @if (itemEditorUpdate() && itemCode() === item.code) {\r\n            <tr>\r\n              <td class="border-l-4 px-2" colspan="4">\r\n                <app-enum-editor\r\n                  mode="update"\r\n                  (update)="afterUpdateItem($event)"\r\n                  [art]="art()"\r\n                  [(visible)]="itemEditorUpdate"\r\n                  [item]="item"\r\n                />\r\n              </td>\r\n            </tr>\r\n          }\r\n        } @empty {\r\n          <tr>\r\n            <td class="px-2" colspan="4">No items</td>\r\n          </tr>\r\n        }\r\n      </tbody>\r\n    </table>\r\n  }\r\n</div>\r\n' }]
  }], null, { art: [{ type: Input, args: [{ isSignal: true, alias: "art", required: true }] }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(EnumListerComponent, { className: "EnumListerComponent", filePath: "src/main/angular/pages/basis/enum-lister/enum-lister.ts", lineNumber: 27 });
})();

// src/main/angular/pages/basis/enum.routes.ts
var routes = [
  { path: ":art", component: EnumListerComponent, providers: [EnumService] }
];
export {
  routes
};
//# sourceMappingURL=chunk-XOGXSRHF.js.map
