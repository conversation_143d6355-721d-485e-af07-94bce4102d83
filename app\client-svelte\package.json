{"name": "client", "version": "1.2.0", "scripts": {"build": "vite build", "start": "vite serve --port 5050", "test": "vitest run", "e2e": "npx playwright test", "e2e-headed": "npx playwright test --headed", "e2e-codegen": "npx playwright codegen localhost:5050", "e2e-install": "npx playwright install", "prettierApply": "npx prettier --write src", "prettierCheck": "npx prettier --check src"}, "devDependencies": {"@playwright/test": "1.55.0", "@sveltejs/vite-plugin-svelte": "6.2.1", "@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "4.1.16", "@tailwindcss/vite": "4.1.16", "chance": "1.1.13", "dotenv": "17.2.3", "page": "1.11.6", "prettier": "3.6.2", "prettier-plugin-svelte": "3.4.0", "svelte": "5.42.2", "tailwindcss": "4.1.16", "uuid": "13.0.0", "vite": "7.1.12", "vitest": "4.0.4"}, "type": "module"}