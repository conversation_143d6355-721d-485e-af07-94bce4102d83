<h1>Vet</h1>

<div class="flex flex-col gap-1 ml-2 mr-2">
  <form [formGroup]="filterForm" (ngSubmit)="onFilterClicked()">
    <div class="flex flex-row gap-2 items-center pb-2 pr-2">
      <input
        aria-label="Filter"
        type="text"
        class="input input-bordered w-full"
        placeholder="Enter filter critria"
        formControlName="criteria"
      />
      <button
        type="submit"
        title="Filter items"
        class="btn btn-circle btn-outline"
        [disabled]="vetFilterDisabled()"
      >
        <span class="material-icons">search</span>
      </button>
    </div>
  </form>
  @if (loading()) {
    <div class="h-screen flex justify-center items-start">
      <span class="loading loading-spinner loading-xl"></span>
    </div>
  } @else {
    <table class="table-fixed">
      <thead class="justify-between">
        <tr class="bg-gray-200">
          <th class="px-2 py-3 text-left w-1/3 table-cell">
            <span class="text-gray-600">Name</span>
          </th>
          <th class="px-2 py-3 text-left w-full table-cell">
            <span class="text-gray-600">Skills</span>
          </th>
          <th class="px-2 py-3 text-right w-0 table-cell">
            <button
              title="Add a new vet"
              class="btn btn-circle btn-outline"
              (click)="onVetEditorCreateClicked()"
              [disabled]="vetEditorDisabled()"
            >
              <span class="material-icons">add</span>
            </button>
          </th>
        </tr>
      </thead>
      <tbody>
        @if (vetEditorCreate()) {
          <tr>
            <td class="px-2" colspan="3">
              <app-vet-editor
                mode="create"
                (create)="afterCreateVet($event)"
                [(visible)]="vetEditorCreate"
                [allSkillEnum]="allSkillEnum()"
                [vet]="newVet()"
              />
            </td>
          </tr>
        }
        @for (vet of allVet(); track vet.id) {
          <tr
            [title]="vet.name"
            [class.border-l-2]="vetId() === vet.id"
            [class.bg-gray-100]="$index % 2 === 1"
            (click)="onVetClicked(vet)"
          >
            <td class="px-2 py-3 text-left table-cell">
              <span>{{ vet.name }}</span>
            </td>
            <td class="px-2 py-3 text-left table-cell">
              @for (skill of vet.allSkill; track skill) {
                <div class="flex flex-col">
                  <span>{{ skill }}</span>
                </div>
              } @empty {
                <span>No skills</span>
              }
            </td>
            <td class="px-2 py-3 table-cell">
              <div
                class="grid grid-cols-1 md:grid-cols-2 items-center gap-1 w-max"
              >
                <button
                  title="Delete a vet"
                  class="btn btn-circle btn-outline"
                  (click)="onVetRemoveClicked(vet)"
                  [disabled]="vetEditorDisabled()"
                >
                  <span class="material-icons">delete</span>
                </button>
                <button
                  title="Edit a vet"
                  class="btn btn-circle btn-outline"
                  (click)="onVetEditorUpdateClicked(vet)"
                  [disabled]="vetEditorDisabled()"
                >
                  <span class="material-icons">edit</span>
                </button>
              </div>
            </td>
          </tr>
          @if (vetEditorUpdate() && vetId() === vet.id) {
            <tr>
              <td class="border-l-4 px-2" colspan="3">
                <app-vet-editor
                  mode="update"
                  (update)="afterUpdateVet($event)"
                  [(visible)]="vetEditorUpdate"
                  [allSkillEnum]="allSkillEnum()"
                  [vet]="vet"
                />
              </td>
            </tr>
          }
        } @empty {
          <tr>
            <td class="px-2" colspan="3">No vets</td>
          </tr>
        }
      </tbody>
    </table>
  }
</div>
