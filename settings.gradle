pluginManagement {
    plugins {
        id 'io.spring.dependency-management' version '1.1.7'
        id 'org.springframework.boot' version '3.5.7'
        id 'com.diffplug.spotless' version '8.0.0'
        id 'com.palantir.git-version' version '4.1.0'
        id 'com.github.node-gradle.node' version '7.1.0'
    }
    repositories {
        gradlePluginPortal()
        mavenCentral()
    }
}

dependencyResolutionManagement {
    versionCatalogs {
        extraLibs {
            // https://playwright.dev/java/
            library('playwright',
                    'com.microsoft.playwright:playwright:1.55.0')
        }
    }
}

include 'lib:backend-api'
include 'lib:backend-data'
include 'app:server'
include 'app:client-svelte'
include 'app:client-angular'
include 'app:deploy'
