import { Component, signal } from "@angular/core";
import { RouterLink, RouterOutlet } from "@angular/router";
import { AppLogoComponent } from "./controls/app-logo/app-logo";
import { AppIconComponent } from "./controls/app-icon/app-icon";

@Component({
  selector: "app-root",
  imports: [AppLogoComponent, AppIconComponent, RouterLink, RouterOutlet],
  templateUrl: "./app.html",
  styleUrl: "./app.css",
})
export class App {
  protected menuVisible = signal(false);
  onMenuToggle() {
    this.menuVisible.update((value) => !value);
  }
  onMenuClose() {
    this.menuVisible.set(false);
  }
}
