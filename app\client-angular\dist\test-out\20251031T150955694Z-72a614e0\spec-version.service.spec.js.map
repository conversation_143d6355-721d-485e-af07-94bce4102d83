{"version": 3, "sources": ["src/main/angular/services/version.service.spec.ts"], "sourcesContent": ["import { describe, it, expect, vi, beforeEach } from \"vitest\";\nimport { provideZonelessChangeDetection } from \"@angular/core\";\nimport { TestBed } from \"@angular/core/testing\";\nimport { HttpClient } from \"@angular/common/http\";\nimport { of, throwError } from \"rxjs\";\nimport { VersionService } from \"./version.service\";\nimport { type Version } from \"../types/version.type\";\n\nconst VERSION: Version = {\n  version: \"1.2.0\",\n};\n\ndescribe(\"VersionService\", () => {\n  let versionService: VersionService;\n  let httpClientMock: any;\n\n  beforeEach(() => {\n    httpClientMock = {\n      get: vi.fn(),\n    };\n    TestBed.configureTestingModule({\n      providers: [\n        VersionService,\n        { provide: HttpClient, useValue: httpClientMock },\n        provideZonelessChangeDetection(),\n      ],\n    });\n    versionService = TestBed.inject(VersionService);\n  });\n\n  it(\"should be created\", () => {\n    expect(versionService).toBeTruthy();\n    expect(versionService[\"httpClient\"]).toBeDefined();\n  });\n\n  describe(\"loadVersion\", () => {\n    it(\"should load version successfully\", () => {\n      httpClientMock.get.mockReturnValue(of(VERSION));\n      versionService.loadVersion().subscribe({\n        next: (body) => {\n          expect(body).toEqual(VERSION);\n        },\n      });\n    });\n\n    it(\"should handle errors gracefully\", () => {\n      const notFoundError = {\n        status: 404,\n        message: \"Not Found\",\n      };\n      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));\n      versionService.loadVersion().subscribe({\n        error: (err) => {\n          expect(err).toBe(notFoundError);\n        },\n      });\n    });\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,UAAU,IAAI,QAAQ,IAAI,kBAAkB;AAQrD,IAAM,UAAmB;EACvB,SAAS;;AAGX,SAAS,kBAAkB,MAAK;AAC9B,MAAI;AACJ,MAAI;AAEJ,aAAW,MAAK;AACd,qBAAiB;MACf,KAAK,GAAG,GAAE;;AAEZ,YAAQ,uBAAuB;MAC7B,WAAW;QACT;QACA,EAAE,SAAS,YAAY,UAAU,eAAc;QAC/C,+BAA8B;;KAEjC;AACD,qBAAiB,QAAQ,OAAO,cAAc;EAChD,CAAC;AAED,KAAG,qBAAqB,MAAK;AAC3B,WAAO,cAAc,EAAE,WAAU;AACjC,WAAO,eAAe,YAAY,CAAC,EAAE,YAAW;EAClD,CAAC;AAED,WAAS,eAAe,MAAK;AAC3B,OAAG,oCAAoC,MAAK;AAC1C,qBAAe,IAAI,gBAAgB,GAAG,OAAO,CAAC;AAC9C,qBAAe,YAAW,EAAG,UAAU;QACrC,MAAM,CAAC,SAAQ;AACb,iBAAO,IAAI,EAAE,QAAQ,OAAO;QAC9B;OACD;IACH,CAAC;AAED,OAAG,mCAAmC,MAAK;AACzC,YAAM,gBAAgB;QACpB,QAAQ;QACR,SAAS;;AAEX,qBAAe,IAAI,gBAAgB,WAAW,MAAM,aAAa,CAAC;AAClE,qBAAe,YAAW,EAAG,UAAU;QACrC,OAAO,CAAC,QAAO;AACb,iBAAO,GAAG,EAAE,KAAK,aAAa;QAChC;OACD;IACH,CAAC;EACH,CAAC;AACH,CAAC;", "names": []}