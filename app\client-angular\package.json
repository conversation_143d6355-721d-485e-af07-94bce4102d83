{"name": "client-angular", "version": "1.2.0", "scripts": {"ng": "ng", "start": "ng serve --port 5052", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "e2e": "npx playwright test", "e2e-headed": "npx playwright test --headed", "e2e-codegen": "npx playwright codegen localhost:5052", "e2e-install": "npx playwright install", "prettierApply": "npx prettier --write src", "prettierCheck": "npx prettier --check src"}, "private": true, "dependencies": {"@angular/common": "20.3.7", "@angular/compiler": "20.3.7", "@angular/core": "20.3.7", "@angular/forms": "20.3.7", "@angular/platform-browser": "20.3.7", "@angular/router": "20.3.7", "@tailwindcss/postcss": "4.1.16", "postcss": "8.5.6", "rxjs": "7.8.2", "tailwindcss": "4.1.16", "tslib": "2.8.1"}, "devDependencies": {"@angular/build": "20.3.7", "@angular/cli": "20.3.7", "@angular/compiler-cli": "20.3.7", "@playwright/test": "1.55.0", "chance": "1.1.13", "daisyui": "5.3.10", "typescript": "5.9.3"}}