import {
  TestBed
} from "./chunk-L64EMMEB.js";
import {
  HttpClient,
  VersionService
} from "./chunk-KV72QDNM.js";
import "./chunk-PRSR237C.js";
import {
  of,
  throwError
} from "./chunk-XJIM24FK.js";

// src/main/angular/services/version.service.spec.ts
import { describe, it, expect, vi, beforeEach } from "vitest";
vi.mock("../app.routes", () => ({
  backendUrl: () => "http://localhost:8080"
}));
vi.mock("../utils/log", () => ({
  tapLog: () => (source) => source
}));
describe("VersionService", () => {
  let service;
  let httpClientMock;
  const mockVersion = {
    version: "1.2.0"
  };
  beforeEach(() => {
    httpClientMock = {
      get: vi.fn()
    };
    TestBed.configureTestingModule({
      providers: [
        VersionService,
        { provide: HttpClient, useValue: httpClientMock }
      ]
    });
    service = TestBed.inject(VersionService);
  });
  it("should be created", () => {
    expect(service).toBeTruthy();
    expect(service["httpClient"]).toBeDefined();
  });
  describe("loadVersion", () => {
    it("should load version successfully", (done) => {
      httpClientMock.get.mockReturnValue(of(mockVersion));
      service.loadVersion().subscribe({
        next: (body) => {
          expect(httpClientMock.get).toHaveBeenCalledWith("http://localhost:8080/version");
          expect(body).toEqual(mockVersion);
        }
      });
    });
    it("should handle errors gracefully", (done) => {
      const notFoundError = {
        status: 404,
        message: "Not Found"
      };
      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));
      service.loadVersion().subscribe({
        error: (err) => {
          expect(httpClientMock.get).toHaveBeenCalledWith("http://localhost:8080/version");
          expect(err).toBe(notFoundError);
        }
      });
    });
  });
});
//# sourceMappingURL=spec-version.service.spec.js.map
