{"version": 3, "sources": ["src/main/angular/services/version.service.spec.ts"], "sourcesContent": ["import { describe, it, expect, vi, beforeEach } from \"vitest\";\nimport { TestBed } from \"@angular/core/testing\";\nimport { HttpClient } from \"@angular/common/http\";\nimport { of, throwError } from \"rxjs\";\nimport { VersionService } from \"./version.service\";\nimport { Version } from \"../types/version.type\";\n\nvi.mock(\"../app.routes\", () => ({\n  backendUrl: () => \"http://localhost:8080\",\n}));\n\nvi.mock(\"../utils/log\", () => ({\n  tapLog: () => (source: any) => source,\n}));\n\ndescribe(\"VersionService\", () => {\n  let service: VersionService;\n  let httpClientMock: any;\n\n  const mockVersion: Version = {\n    version: \"1.2.0\",\n  };\n\n  beforeEach(() => {\n    // Create a mock HttpClient\n    httpClientMock = {\n      get: vi.fn(),\n    };\n\n    // Configure TestBed\n    TestBed.configureTestingModule({\n      providers: [\n        VersionService,\n        { provide: HttpClient, useValue: httpClientMock },\n      ],\n    });\n\n    service = TestBed.inject(VersionService);\n  });\n\n  it(\"should be created\", () => {\n    expect(service).toBeTruthy();\n    expect(service[\"httpClient\"]).toBeDefined();\n  });\n\n  describe(\"loadVersion\", () => {\n    it(\"should load version successfully\", (done) => {\n      httpClientMock.get.mockReturnValue(of(mockVersion));\n      service.loadVersion().subscribe({\n        next: (body) => {\n          // Assert\n          expect(httpClientMock.get).toHaveBeenCalledWith(\n            \"http://localhost:8080/version\"\n          );\n          expect(body).toEqual(mockVersion);\n        },\n      });\n    });\n\n    it(\"should handle errors gracefully\", (done) => {\n      const notFoundError = {\n        status: 404,\n        message: \"Not Found\",\n      };\n      httpClientMock.get.mockReturnValue(throwError(() => notFoundError));\n      service.loadVersion().subscribe({\n        error: (err) => {\n          expect(httpClientMock.get).toHaveBeenCalledWith(\n            \"http://localhost:8080/version\"\n          );\n          expect(err).toBe(notFoundError);\n        },\n      });\n    });\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,UAAU,IAAI,QAAQ,IAAI,kBAAkB;AAOrD,GAAG,KAAK,iBAAiB,OAAO;EAC9B,YAAY,MAAM;EAClB;AAEF,GAAG,KAAK,gBAAgB,OAAO;EAC7B,QAAQ,MAAM,CAAC,WAAgB;EAC/B;AAEF,SAAS,kBAAkB,MAAK;AAC9B,MAAI;AACJ,MAAI;AAEJ,QAAM,cAAuB;IAC3B,SAAS;;AAGX,aAAW,MAAK;AAEd,qBAAiB;MACf,KAAK,GAAG,GAAE;;AAIZ,YAAQ,uBAAuB;MAC7B,WAAW;QACT;QACA,EAAE,SAAS,YAAY,UAAU,eAAc;;KAElD;AAED,cAAU,QAAQ,OAAO,cAAc;EACzC,CAAC;AAED,KAAG,qBAAqB,MAAK;AAC3B,WAAO,OAAO,EAAE,WAAU;AAC1B,WAAO,QAAQ,YAAY,CAAC,EAAE,YAAW;EAC3C,CAAC;AAED,WAAS,eAAe,MAAK;AAC3B,OAAG,oCAAoC,CAAC,SAAQ;AAC9C,qBAAe,IAAI,gBAAgB,GAAG,WAAW,CAAC;AAClD,cAAQ,YAAW,EAAG,UAAU;QAC9B,MAAM,CAAC,SAAQ;AAEb,iBAAO,eAAe,GAAG,EAAE,qBACzB,+BAA+B;AAEjC,iBAAO,IAAI,EAAE,QAAQ,WAAW;QAClC;OACD;IACH,CAAC;AAED,OAAG,mCAAmC,CAAC,SAAQ;AAC7C,YAAM,gBAAgB;QACpB,QAAQ;QACR,SAAS;;AAEX,qBAAe,IAAI,gBAAgB,WAAW,MAAM,aAAa,CAAC;AAClE,cAAQ,YAAW,EAAG,UAAU;QAC9B,OAAO,CAAC,QAAO;AACb,iBAAO,eAAe,GAAG,EAAE,qBACzB,+BAA+B;AAEjC,iBAAO,GAAG,EAAE,KAAK,aAAa;QAChC;OACD;IACH,CAAC;EACH,CAAC;AACH,CAAC;", "names": []}